"use client"

import React, { useLayoutEffect } from "react"
import { getCssText } from "@module/apricot/theme/stitches"

export default function StitchesRegistry({ children }: { children: React.ReactNode }) {
  useLayoutEffect(() => {
    // Remove any existing stitches CSS to avoid conflicts
    const existingStyle = document.getElementById('stitches-client')
    if (existingStyle) {
      existingStyle.remove()
    }

    // Inject CSS synchronously before paint
    const style = document.createElement('style')
    style.id = 'stitches-client'
    style.innerHTML = getCssText()
    document.head.appendChild(style)
  }, [])

  return <>{children}</>
}

"use client"

import { ThemeProvider } from "@module/apricot/theme/provider"
import { TextLockupProvider } from "@module/apricot/components/text-lockup"
import { InterfaceIconsProvider } from "@module/apricot/components/interface-icons"
import RendererProvider from "@cms/rich-text"
import { CartProvider } from "@module/commerce/ui/cart/CartContext"
import { transformData } from "@module/text-lockup/common/transformData"
import customTheme from "@module/style/theme.css"
import {
  Button,
  Heading,
  IconCardSetRoot,
  IconCardSetCard,
  CardIcon,
  CardIconWrapper,
  IconButton,
  HeroContentColumn,
  HeroBanner,
  LockupCaption,
  FiftyFiftyBanner,
  NavLink,
  GlobalNavigation,
  MobileTrigger,
  MobileDrawer,
  MobileNavLink,
  MobileMainMenu,
  ActionMenu,
  MobileActionMenu,
  FooterCopyright,
  FooterLegalMenu,
  FooterLinkList,
  FooterBlocksRoot,
  FooterSocialMenu,
  Grid,
  LandingContent,
  LandingImageWrapper,
  LockupBody,
  TabTrigger,
  RichText,
  LandingBanner,
  BrandWrapper,
  LockupHeading,
  ImageCardSetCard,
} from "@module/style/components.css"

export default function ClientProviders({ children }: { children: React.ReactNode }) {
  return (
    <CartProvider>
      <div className={customTheme.toString()}>
        <TextLockupProvider transformData={transformData}>
          <ThemeProvider
            components={{
              Heading,
              Button,
              IconButton,
              IconCardSet: IconCardSetRoot,
              "IconCardSet.Card": IconCardSetCard,
              "Card.Icon": CardIcon,
              "Card.IconWrapper": CardIconWrapper,
              "Banner.Hero": HeroBanner,
              "Banner.HeroContentColumn": HeroContentColumn,
              "TextLockup.Caption": LockupCaption,
              "TextLockup.Body": LockupBody,
              "Banner.FiftyFifty": FiftyFiftyBanner,
              "GlobalNavigation.NavLink": NavLink,
              GlobalNavigation,
              "GlobalNavigation.MobileTrigger": MobileTrigger,
              "GlobalNavigation.MobileDrawer": MobileDrawer,
              "GlobalNavigation.MobileNavLink": MobileNavLink,
              "GlobalNavigation.MobileMainMenu": MobileMainMenu,
              "GlobalNavigation.ActionMenu": ActionMenu,
              "GlobalNavigation.MobileActionMenu": MobileActionMenu,
              "GlobalNavigation.BrandWrapper": BrandWrapper,
              "Footer.Copyright": FooterCopyright,
              "Footer.LegalMenu": FooterLegalMenu,
              "Footer.LinkList": FooterLinkList,
              "Footer.BlocksRoot": FooterBlocksRoot,
              "Footer.SocialMenu": FooterSocialMenu,
              Grid,
              "Banner.Landing": LandingBanner,
              "Banner.Landing.ImageWrapper": LandingImageWrapper,
              "Banner.Landing.Content": LandingContent,
              "Tabs.Trigger": TabTrigger,
              RichText,
              "TextLockup.Heading": LockupHeading,
              "ImageCardSet.Card": ImageCardSetCard,
            }}
          >
            <InterfaceIconsProvider>
              <RendererProvider>{children}</RendererProvider>
            </InterfaceIconsProvider>
          </ThemeProvider>
        </TextLockupProvider>
      </div>
    </CartProvider>
  )
}

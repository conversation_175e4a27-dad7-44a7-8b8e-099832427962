import ProgressBar from "./components/ProgressBar"
import ClientProviders from "./components/ClientProviders"
import { getCssText } from "@module/apricot/theme/stitches"
import customTheme from "@module/style/theme.css"
import { GTMDataLayer, GTMHead, GTMBody } from "@module/analytics/GoogleAnalytics"

// Generate CSS with custom theme applied
function getThemedCssText() {
  // Apply custom theme class and get CSS
  const baseCSS = getCssText()
  const themeCSS = customTheme.toString()
  return baseCSS + themeCSS
}
import { isUAT } from "@module/next/utility/is-uat"
import { Analytics } from "@vercel/analytics/react"
import "../styles/global.css"
import "keen-slider/keen-slider.min.css"
import "@module/style/css/font-face.css"
import StitchesRegistry from "./components/StitchesRegistry"

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                var script = document.createElement('script');
                script.src = 'https://cdn-4.convertexperiments.com/v1/js/100413023-100413994.js';
                script.async = true; // Optional: set to true for non-blocking load
                document.head.appendChild(script);
              })();
            `,
          }}
        />
        <GTMDataLayer />
        <GTMHead />
        <link href="https://api.mapbox.com/mapbox-gl-js/v2.7.0/mapbox-gl.css" rel="stylesheet" />
        <style id="stitches-server" dangerouslySetInnerHTML={{ __html: getThemedCssText() }} />
        {isUAT && <meta name="robots" content="noindex, nofollow" />}
      </head>
      <body>
        <GTMBody />
        <ProgressBar />
        <ClientProviders>
          <StitchesRegistry>{children}</StitchesRegistry>
        </ClientProviders>
        {process.env.VERCEL_ENV === "production" && <Analytics />}
      </body>
    </html>
  )
}

import { LocationCardIntegration } from "@module/location/service"
import { MentorCardIntegration } from "@module/mentor"
import { QueryProperties, QueryResponse } from "@module/interactive-map/controller/api/listViewApi/types"

export const transformInitialResponse = (response: QueryResponse): QueryProperties => {
  const { allLocations = [], _allLocationsMeta, allMentors = [], _allMentorsMeta } = response
  return {
    allLocations: allLocations.map((value) => ({
      id: value.id,
      ...LocationCardIntegration.transformQueryData(value),
    })),
    _allLocationsMeta: _allLocationsMeta || { count: 0 },
    allMentors: allMentors.map((value) => ({
      id: value.id,
      ...MentorCardIntegration.transformQueryData(value),
    })),
    _allMentorsMeta: _allMentorsMeta || { count: 0 },
  }
}

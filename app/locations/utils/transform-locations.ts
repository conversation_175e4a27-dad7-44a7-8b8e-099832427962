import { CachedLocationsStruct } from "@module/redux/common/types/cached-locations"
import { InteractiveMap } from "@module/interactive-map/common/types"

const getDeliveryType = (location: CachedLocationsStruct.Location) => {
  return {
    isOnCampus: location.faceToFaceOnCampus,
    isWithAIPTMentor: location.withAiptMentor,
  }
}

export const transformLocationsToMapLocations = (
  locations: CachedLocationsStruct.Location[],
): InteractiveMap.MapLocation[] => {
  return locations.map((location) => {
    const { isOnCampus, isWithAIPTMentor } = getDeliveryType(location)
    return {
      id: location.id,
      location: {
        latitude: location.location.latitude,
        longitude: location.location.longitude,
      },
      coursesOffered: location.coursesOffered,
      state: location.state as InteractiveMap.State,
      recordType: "Location",
      isOnCampus,
      isWithAIPTMentor,
    }
  })
}

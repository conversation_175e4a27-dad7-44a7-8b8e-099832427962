import { createClient } from "../../../lib/datocms"
import { FILTERED_LOCATIONS_QUERY, LOCATIONS_QUERY } from "@module/interactive-map/controller/api/listViewApi/queries"
import {
  transformFilteredLocationResponse,
  transformLocationResponse,
} from "@module/interactive-map/controller/api/listViewApi/transformResponse"
import { ONE_HUNDRED_KMS } from "@module/mapbox/common/constants"
import { getDeliveryType } from "@module/interactive-map/common/deliveryType"
import type { ParsedLocationsSearchParams } from "../types"

export async function fetchFilteredLocationsSSR(searchParams: ParsedLocationsSearchParams, perPage: number = 12) {
  const client = createClient()
  const { course, state, isOnCampus, isWithAIPTMentor, includeSurroundingArea, page, location } = searchParams

  // Check if we need filtering
  const hasFilters =
    course.length > 0 || state !== "" || location !== null || !isOnCampus || !isWithAIPTMentor || includeSurroundingArea

  // If no filters are applied, use the simple query
  if (!hasFilters) {
    const data = await client.request(LOCATIONS_QUERY, {
      page: (page - 1) * perPage,
      perPage,
    })
    return transformLocationResponse(data as any, {}, { page: page - 1, perPage })
  }

  // Use filtered query for complex filtering
  const deliveryType = getDeliveryType({ isOnCampus, isWithAIPTMentor })
  const query = FILTERED_LOCATIONS_QUERY(isOnCampus, isWithAIPTMentor)

  const variables: any = {
    page: (page - 1) * perPage,
    perPage,
    courses: course,
    state: state ? { in: [state] } : {},
    location:
      location === null || state
        ? {}
        : {
            near: {
              latitude: location.latitude,
              longitude: location.longitude,
              radius: includeSurroundingArea ? ONE_HUNDRED_KMS * 2 : ONE_HUNDRED_KMS,
            },
            exists: true,
          },
  }

  // Add delivery type variables if needed
  if (deliveryType !== "none") {
    if (deliveryType === "onCampus" || deliveryType === "mixed") {
      variables.isOnCampus = { eq: isOnCampus }
    }
    if (deliveryType === "withAIPTMentor" || deliveryType === "mixed") {
      variables.isWithAIPTMentor = { eq: isWithAIPTMentor }
    }
  }

  const data = await client.request(query, variables)
  return transformFilteredLocationResponse(
    data as any,
    {},
    {
      page: page - 1,
      perPage,
      courses: course,
      includeSurroundingAreas: includeSurroundingArea,
      isOnCampus,
      isWithAIPTMentor,
      location,
      state: state || undefined,
    },
  )
}

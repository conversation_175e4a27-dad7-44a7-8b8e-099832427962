import React from "react"
import { Metada<PERSON> } from "next"
import { redirect } from "next/navigation"
import getGlobalFooter from "@cms/global-footer"
import getGlobalNavigation from "@cms/global-navigation"
import { getLocationLandingData } from "@cms/location-landing"
import getEnquireForm from "@cms/global-enquire-form"
import FetchAllAvailableLocations from "../../../../cms/dato/location/service/fetch-all-available-locations"
import { SITE_SCHEMA } from "@module/utilities/seo-schema"
import { siteName } from "../../constants"
import LocationsPageClient from "../../_components/LocationsPageClient"
import { transformLocationsToMapLocations } from "../../utils/transform-locations"
import FetchAllAvailableMentors from "../../../../cms/dato/mentor/service/fetch-all-available-mentors"
import { InteractiveMap } from "@module/interactive-map/common/types"
import { LocationsSearchParams, parseLocationSearchParams } from "../../types"
import { fetchFilteredLocationsSSR } from "../../utils/server-side-data-fetching"

interface PageProps {
  params: Promise<{ number: string }>
  searchParams: Promise<LocationsSearchParams>
}

export async function generateMetadata({ params, searchParams }: PageProps): Promise<Metadata> {
  const { data } = await getLocationLandingData()
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Parse page number
  const pageNumber = parseInt(resolvedParams.number, 10)
  if (isNaN(pageNumber) || pageNumber < 1) {
    redirect("/locations")
  }

  // Parse search params with page number
  const parsedParams = parseLocationSearchParams({ ...resolvedSearchParams, page: pageNumber.toString() })

  // Create dynamic title based on filters and page
  let title = data.seo.title
  let description = data.seo.description

  // Add page number to title if not page 1
  if (pageNumber > 1) {
    title = `${title} - Page ${pageNumber}`
  }

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      url: `/locations/pages/${pageNumber}`,
    },
  }
}

export default async function LocationsPage({ params, searchParams }: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  // Parse and validate page number
  const pageNumber = parseInt(resolvedParams.number, 10)
  if (isNaN(pageNumber) || pageNumber < 1) {
    redirect("/locations")
  }

  // Remove page param from query string since it's in the URL path
  const { page: _, ...searchParamsWithoutPage } = resolvedSearchParams

  // Parse search params with page number from URL path
  const parsedParams = parseLocationSearchParams({ ...searchParamsWithoutPage, page: pageNumber.toString() })

  const [{ data }, navigation, footer, enquireFormProperties, allLocations, allMentors, initialLocationsData] =
    await Promise.all([
      getLocationLandingData(),
      getGlobalNavigation(),
      getGlobalFooter(),
      getEnquireForm(),
      new FetchAllAvailableLocations().execute(),
      new FetchAllAvailableMentors().execute(),
      fetchFilteredLocationsSSR(parsedParams, 12),
    ])

  // Transform locations for map display
  const transformedLocations = transformLocationsToMapLocations(allLocations)

  // Transform mentors for map display
  const transformedMentors = allMentors.map((mentor) => ({
    id: mentor.id,
    location: mentor.location,
    coursesOffered: mentor.qualifications ? mentor.qualifications.map((q) => q.slug) : [],
    state: (mentor as any).state as InteractiveMap.State,
    recordType: "Mentor" as const,
    isOnCampus: false,
    isWithAIPTMentor: true,
  }))

  const schema = SITE_SCHEMA

  return (
    <LocationsPageClient
      data={data}
      navigation={navigation}
      footer={footer}
      enquireFormProperties={enquireFormProperties}
      locations={transformedLocations}
      mentors={transformedMentors}
      initialLocations={JSON.stringify(initialLocationsData)}
      siteName={siteName}
      schema={schema}
      currentPage={parsedParams.page}
      totalPages={Math.ceil((initialLocationsData._allLocationsMeta?.count || 0) / 12)}
      totalLocations={initialLocationsData._allLocationsMeta?.count || 0}
      initialSearchParams={{
        course: parsedParams.course,
        state: parsedParams.state,
        isOnCampus: parsedParams.isOnCampus,
        isWithAIPTMentor: parsedParams.isWithAIPTMentor,
        includeSurroundingArea: parsedParams.includeSurroundingArea,
        location: parsedParams.location,
        viewMode: parsedParams.viewMode,
        page: 1, // Always use page 1 for initialSearchParams since actual page is in URL path
      }}
    />
  )
}

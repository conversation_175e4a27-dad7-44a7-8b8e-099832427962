import { notFound } from "next/navigation"
import { Metada<PERSON> } from "next"

import getGlobalFooter from "@cms/global-footer"
import getGlobalNavigation from "@cms/global-navigation"
import getLocationPage, { getLocationPagePaths } from "@cms/location"
import getEnquireForm from "@cms/global-enquire-form"

import processCatchAllSlug from "@module/next/utility/process-catch-all-slug"
import { injectPathUrl } from "@module/seo/service/inject-path-url"

import { siteName } from "apricot.config"
import LocationSlugPageClient from "../_components/LocationSlugPageClient"

interface LocationPageProps {
  params: Promise<{
    slug: string[]
  }>
}

export default async function LocationPage({ params }: LocationPageProps) {
  const resolvedParams = await params

  if (!resolvedParams?.slug || resolvedParams.slug.length === 0) {
    notFound()
  }

  const slug = processCatchAllSlug(resolvedParams.slug)

  try {
    const [{ data }, navigation, footer, enquireFormProperties] = await Promise.all([
      getLocationPage(slug),
      getGlobalNavigation(),
      getGlobalFooter(),
      getEnquireForm(),
    ])

    const locationData = injectPathUrl(data, `locations/${slug}`)

    return (
      <LocationSlugPageClient
        locationData={locationData}
        navigation={navigation}
        footer={footer}
        enquireFormProperties={enquireFormProperties}
      />
    )
  } catch (error) {
    console.error("Error loading location page:", error)
    notFound()
  }
}

export async function generateMetadata({ params }: LocationPageProps): Promise<Metadata> {
  const resolvedParams = await params

  if (!resolvedParams?.slug || resolvedParams.slug.length === 0) {
    return {}
  }

  const slug = processCatchAllSlug(resolvedParams.slug)

  try {
    const { data } = await getLocationPage(slug)
    const locationData = injectPathUrl(data, `locations/${slug}`)

    return {
      title: locationData.seo?.title,
      description: locationData.seo?.description,
      openGraph: {
        title: locationData.seo?.title,
        description: locationData.seo?.description,
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/locations/${slug}`,
        siteName,
        images: locationData.seo?.image
          ? [
              {
                url: locationData.seo.image.url,
              },
            ]
          : [],
      },
      twitter: {
        card: "summary_large_image",
        title: locationData.seo?.title,
        description: locationData.seo?.description,
        images: locationData.seo?.image?.url ? [locationData.seo.image.url] : [],
      },
      alternates: {
        canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/locations/${slug}`,
      },
    }
  } catch (error) {
    console.error("Error generating metadata for location:", error)
    return {}
  }
}

export async function generateStaticParams() {
  try {
    const paths = await getLocationPagePaths()

    return paths.paths.map((path) => ({
      slug: path.params.slug,
    }))
  } catch (error) {
    console.error("Error generating static params for locations:", error)
    return []
  }
}

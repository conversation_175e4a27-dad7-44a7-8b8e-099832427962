import React from "react"
import { Box } from "@module/apricot/components/box"
import { SectionWrapper } from "@module/apricot/components/section-wrapper"
import { TextLockup } from "@module/apricot/components/text-lockup"

export default function NotFound() {
  return (
    <SectionWrapper>
      <Box css={{ textAlign: "center", padding: "$xl" }}>
        <TextLockup
          heading="Page Not Found"
          headingLevel="h1"
          headingSize="h1"
          description="Sorry, we couldn't find the page you're looking for."
          alignment="center"
          links={[
            {
              __typename: "SmarkLink",
              id: "back-to-locations",
              title: "Back to Locations",
              internalLink: {
                slug: "/locations",
                promoteAsHomepage: false,
              },
            },
          ]}
        />
      </Box>
    </SectionWrapper>
  )
}

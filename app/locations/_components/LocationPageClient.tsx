"use client"

import React from "react"
import { Box } from "@module/apricot/components/box"
import { GlobalNavigation } from "@module/global-navigation"
import { Footer } from "@module/footer"
import { Seo } from "@module/seo/ui"
import { EnquireFormProvider } from "@module/enquire-form/service/EnquireFormContext"
import FlexibleContent from "@module/flexible-content"
import { FAQ_SCHEMA, SITE_SCHEMA } from "@module/utilities/seo-schema"
import { LocationHeroBanner } from "@module/banner-location-hero"

interface LocationPageClientProps {
  dataWithUrl: any
  navigation: any
  footer: any
  enquireFormProperties: any
}

export default function LocationPageClient({
  dataWithUrl,
  navigation,
  footer,
  enquireFormProperties,
}: LocationPageClientProps) {
  return (
    <EnquireFormProvider properties={enquireFormProperties}>
      <Box id="top" />
      <GlobalNavigation {...navigation} />
      <Box component="main">
        <LocationHeroBanner {...dataWithUrl.banner} />
        <FlexibleContent data={dataWithUrl.flexibleContent} />
      </Box>
      <Footer {...footer} />
      <Seo
        title={dataWithUrl.seo.title}
        description={dataWithUrl.seo.description}
        pathUrl={dataWithUrl.seo.pathUrl}
        image={dataWithUrl.seo.image}
        twitterCard="summary_large_image"
        schema={[...SITE_SCHEMA, ...FAQ_SCHEMA]}
      />
    </EnquireFormProvider>
  )
}

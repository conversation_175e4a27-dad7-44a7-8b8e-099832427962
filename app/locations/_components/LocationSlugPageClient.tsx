"use client"

import React, { useLayoutEffect } from "react"
import { getCssText } from "@module/apricot/theme/stitches"
import FlexibleContent from "@module/flexible-content"
import { GlobalNavigation } from "@module/global-navigation"
import { Footer } from "@module/footer"
import { LocationHeroBanner } from "@module/banner-location-hero"
import { Seo } from "@module/seo/ui"
import { EnquireFormProvider } from "@module/enquire-form/service/EnquireFormContext"
import { SITE_SCHEMA } from "@module/utilities/seo-schema"

import { siteName } from "apricot.config"

interface LocationSlugPageClientProps {
  locationData: any
  navigation: any
  footer: any
  enquireFormProperties: any
}

export default function LocationSlugPageClient({
  locationData,
  navigation,
  footer,
  enquireFormProperties,
}: LocationSlugPageClientProps) {
  useLayoutEffect(() => {
    console.log('🎯 Campus CSS injection starting...')

    // Remove any existing campus styles first
    const existingStyle = document.getElementById('stitches-campus')
    if (existingStyle) {
      console.log('🗑️ Removing existing campus CSS')
      existingStyle.remove()
    }

    // Force inject CSS with high priority
    const style = document.createElement('style')
    style.id = 'stitches-campus'
    const cssText = getCssText()
    style.innerHTML = cssText

    console.log('💉 Injecting campus CSS, length:', cssText.length)

    // Insert at the end of head for highest priority
    document.head.appendChild(style)

    // Force reflow
    document.body.offsetHeight

    console.log('✅ Campus CSS injection complete')
  }, [])

  return (
    <EnquireFormProvider properties={enquireFormProperties}>
      <Seo {...locationData.seo} schema={SITE_SCHEMA} />
      <GlobalNavigation {...navigation} />
      <LocationHeroBanner {...locationData.banner} />
      <FlexibleContent data={locationData.flexibleContent} />
      <Footer {...footer} reversed siteName={siteName} />
    </EnquireFormProvider>
  )
}

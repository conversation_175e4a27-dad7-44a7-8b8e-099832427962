"use client"

import FlexibleContent from "@module/flexible-content"
import { GlobalNavigation } from "@module/global-navigation"
import { Footer } from "@module/footer"
import { LocationHeroBanner } from "@module/banner-location-hero"
import { Seo } from "@module/seo/ui"
import { EnquireFormProvider } from "@module/enquire-form/service/EnquireFormContext"
import { SITE_SCHEMA } from "@module/utilities/seo-schema"
import StitchesRegistry from "../../components/StitchesRegistry"

import { siteName } from "apricot.config"

interface LocationSlugPageClientProps {
  locationData: any
  navigation: any
  footer: any
  enquireFormProperties: any
}

export default function LocationSlugPageClient({
  locationData,
  navigation,
  footer,
  enquireFormProperties,
}: LocationSlugPageClientProps) {
  return (
    <StitchesRegistry>
      <EnquireFormProvider properties={enquireFormProperties}>
        <Seo {...locationData.seo} schema={SITE_SCHEMA} />
        <GlobalNavigation {...navigation} />
        <LocationHeroBanner {...locationData.banner} />
        <FlexibleContent data={locationData.flexibleContent} />
        <Footer {...footer} reversed siteName={siteName} />
      </EnquireFormProvider>
    </StitchesRegistry>
  )
}

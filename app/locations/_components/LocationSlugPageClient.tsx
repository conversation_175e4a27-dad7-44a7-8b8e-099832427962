"use client"

import React, { useEffect } from "react"
import FlexibleContent from "@module/flexible-content"
import { GlobalNavigation } from "@module/global-navigation"
import { Footer } from "@module/footer"
import { LocationHeroBanner } from "@module/banner-location-hero"
import { Seo } from "@module/seo/ui"
import { EnquireFormProvider } from "@module/enquire-form/service/EnquireFormContext"
import { SITE_SCHEMA } from "@module/utilities/seo-schema"

import { siteName } from "apricot.config"

interface LocationSlugPageClientProps {
  locationData: any
  navigation: any
  footer: any
  enquireFormProperties: any
}

export default function LocationSlugPageClient({
  locationData,
  navigation,
  footer,
  enquireFormProperties,
}: LocationSlugPageClientProps) {
  useEffect(() => {
    console.log('🎯 Campus page mounted')
    console.log('📊 CSS styles in head:', document.head.querySelectorAll('style').length)
    document.head.querySelectorAll('style').forEach((style, index) => {
      console.log(`📝 Style ${index + 1}:`, style.id || 'no-id', style.innerHTML.length, 'chars')
    })
  }, [])

  return (
    <EnquireFormProvider properties={enquireFormProperties}>
      <Seo {...locationData.seo} schema={SITE_SCHEMA} />
      <GlobalNavigation {...navigation} />
      <LocationHeroBanner {...locationData.banner} />
      <FlexibleContent data={locationData.flexibleContent} />
      <Footer {...footer} reversed siteName={siteName} />
    </EnquireFormProvider>
  )
}

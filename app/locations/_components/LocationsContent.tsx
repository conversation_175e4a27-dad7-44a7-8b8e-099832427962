"use client"

import React from "react"
import InteractiveMap from "@module/interactive-map"
import { LocationsContentProps } from "../types"
import type { ParsedLocationsSearchParams } from "../types"

export default function LocationsContent({
  interactiveMapProps,
  initialLocations,
  currentPage,
  totalPages,
  totalLocations,
  initialSearchParams,
}: LocationsContentProps & {
  initialLocations: any
  currentPage?: number
  totalPages?: number
  totalLocations?: number
  initialSearchParams?: ParsedLocationsSearchParams
}) {
  return (
    <InteractiveMap
      {...interactiveMapProps}
      initialLocations={initialLocations}
      currentPage={currentPage}
      totalPages={totalPages}
      totalLocations={totalLocations}
      initialSearchParams={initialSearchParams}
      enableUrlSync={true}
    />
  )
}

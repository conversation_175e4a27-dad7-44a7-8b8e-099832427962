import type { CachedLocationsStruct } from "@module/redux/common/types/cached-locations"
import type { PageDataReturn } from "@cms/location-landing/types"
import { InteractiveMap } from "@module/interactive-map/common/types"

export type InteractiveMapProperties = Omit<PageDataReturn["data"], "introductionBanner" | "flexibleContent" | "seo">

export interface LocationsContentProps {
  interactiveMapProps: InteractiveMapProperties
}

export interface LocationsListProps {
  locations: CachedLocationsStruct.Location[]
  currentPage: number
  totalPages: number
  totalLocations: number
}

export interface LocationsPaginationProps {
  currentPage: number
  totalPages: number
}

export interface LocationsMapClientProps {
  interactiveMapProps: InteractiveMapProperties
}

export interface LocationsPageClientProps {
  data: PageDataReturn["data"]
  navigation: any
  footer: any
  enquireFormProperties: any
  locations?: InteractiveMap.MapLocation[]
  mentors?: InteractiveMap.MapLocation[]
  initialLocations?: string
  siteName: string
  schema: string
  currentPage?: number
  totalPages?: number
  totalLocations?: number
  initialSearchParams?: ParsedLocationsSearchParams
}

export interface LocationsSearchParams {
  course?: string | string[]
  state?: InteractiveMap.State
  isOnCampus?: string
  isWithAIPTMentor?: string
  includeSurroundingArea?: string
  page?: string
  lat?: string
  lng?: string
  // view parameter removed - always start with list view for better performance
}

export interface ParsedLocationsSearchParams {
  course: string[]
  state: InteractiveMap.State | ""
  isOnCampus: boolean
  isWithAIPTMentor: boolean
  includeSurroundingArea: boolean
  page: number
  location: { latitude: number; longitude: number } | null
  viewMode: InteractiveMap.ViewMode
}

export function parseLocationSearchParams(searchParams: LocationsSearchParams): ParsedLocationsSearchParams {
  // Parse course parameter
  const courseParam = searchParams.course
  const course = Array.isArray(courseParam) ? courseParam : courseParam ? [courseParam] : []

  // Parse state parameter
  const state = (searchParams.state as InteractiveMap.State) || ""

  // Parse boolean parameters
  const isOnCampus = searchParams.isOnCampus !== "false"
  const isWithAIPTMentor = searchParams.isWithAIPTMentor !== "false"
  const includeSurroundingArea = searchParams.includeSurroundingArea === "true"

  // Always start with list view on initial render for better performance and SSR
  const viewMode = "list" as InteractiveMap.ViewMode

  // Parse pagination
  const page = Math.max(1, parseInt(searchParams.page || "1", 10))

  // Parse location coordinates
  const lat = searchParams.lat ? parseFloat(searchParams.lat) : null
  const lng = searchParams.lng ? parseFloat(searchParams.lng) : null
  const location = lat && lng ? { latitude: lat, longitude: lng } : null

  return {
    course,
    state,
    isOnCampus,
    isWithAIPTMentor,
    includeSurroundingArea,
    viewMode,
    page,
    location,
  }
}

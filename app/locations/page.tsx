import React from "react"
import { <PERSON>ada<PERSON> } from "next"
import getGlobalFooter from "@cms/global-footer"
import getGlobalNavigation from "@cms/global-navigation"
import { getLocationLandingData } from "@cms/location-landing"
import getEnquireForm from "@cms/global-enquire-form"
import FetchAllAvailableLocations from "../../cms/dato/location/service/fetch-all-available-locations"
import { SITE_SCHEMA } from "@module/utilities/seo-schema"
import { siteName } from "./constants"
import { injectPathUrl } from "@module/seo/service/inject-path-url"
import LocationsPageClient from "./_components/LocationsPageClient"
import { transformLocationsToMapLocations } from "./utils/transform-locations"
import FetchAllAvailableMentors from "../../cms/dato/mentor/service/fetch-all-available-mentors"
import { InteractiveMap } from "@module/interactive-map/common/types"
import { LocationsSearchParams, parseLocationSearchParams } from "./types"
import { fetchFilteredLocationsSSR } from "./utils/server-side-data-fetching"

interface PageProps {
  searchParams: Promise<LocationsSearchParams>
}

export async function generateMetadata({ searchParams }: PageProps): Promise<Metadata> {
  const { data } = await getLocationLandingData()
  const params = await searchParams
  const parsedParams = parseLocationSearchParams(params)

  // Create dynamic title based on filters
  let title = data.seo.title
  let description = data.seo.description

  // Enhance title and description based on applied filters
  const titleParts = [data.seo.title]
  const descriptionParts = []

  if (parsedParams.state) {
    titleParts.push(parsedParams.state)
    descriptionParts.push(`locations in ${parsedParams.state}`)
  }

  if (parsedParams.course.length > 0) {
    const courseNames = parsedParams.course.join(", ")
    titleParts.push(`${courseNames} Courses`)
    descriptionParts.push(`offering ${courseNames} courses`)
  }

  if (!parsedParams.isOnCampus && parsedParams.isWithAIPTMentor) {
    titleParts.push("Personal Training")
    descriptionParts.push("with AIPT mentors")
  } else if (parsedParams.isOnCampus && !parsedParams.isWithAIPTMentor) {
    titleParts.push("On-Campus Training")
    descriptionParts.push("at our campus locations")
  }

  if (parsedParams.page > 1) {
    titleParts.push(`Page ${parsedParams.page}`)
  }

  // Build enhanced title (max 60 chars for SEO)
  title = titleParts.join(" - ")
  if (title.length > 60) {
    title =
      `${titleParts[0]} - ${titleParts[1] || ""} - ${parsedParams.page > 1 ? `Page ${parsedParams.page}` : ""}`.trim()
  }

  // Build enhanced description (max 160 chars for SEO)
  if (descriptionParts.length > 0) {
    description = `Find ${descriptionParts.join(" and ")} near you. ${data.seo.description}`
    if (description.length > 160) {
      description = description.substring(0, 157) + "..."
    }
  }

  const dataWithUrl = injectPathUrl(data, "locations")

  // Create structured data for search engines
  const currentUrl = `${dataWithUrl.seo.pathUrl}${Object.keys(params).length > 0 ? "?" + new URLSearchParams(params as Record<string, string>).toString() : ""}`

  return {
    title,
    description,
    keywords: [
      "fitness courses",
      "personal training",
      "AIPT locations",
      "fitness education",
      ...(parsedParams.state ? [parsedParams.state, `${parsedParams.state} fitness courses`] : []),
      ...(parsedParams.course.length > 0 ? parsedParams.course : []),
    ].join(", "),
    openGraph: {
      title,
      description,
      url: currentUrl,
      type: "website",
      siteName: "Australian Institute of Personal Trainers",
      images: data.seo.image ? [data.seo.image] : [],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: data.seo.image ? [data.seo.image] : [],
    },
    alternates: {
      canonical: currentUrl,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
      },
    },
  }
}

export default async function LocationsPage({ searchParams }: PageProps) {
  const params = await searchParams
  const parsedParams = parseLocationSearchParams(params)

  const [{ data }, navigation, footer, enquireFormProperties] = await Promise.all([
    getLocationLandingData(),
    getGlobalNavigation(),
    getGlobalFooter(),
    getEnquireForm(),
  ])

  const hasBuiltInData = data.allLocations && data.allMentors

  let allLocations, allMentors, initialLocationsData
  if (!hasBuiltInData) {
    const [fetchedLocations, fetchedMentors, fetchedInitialData] = await Promise.all([
      new FetchAllAvailableLocations().execute(),
      new FetchAllAvailableMentors().execute(),
      fetchFilteredLocationsSSR(parsedParams, 12),
    ])
    allLocations = fetchedLocations
    allMentors = fetchedMentors
    initialLocationsData = fetchedInitialData
  }

  const dataWithUrl = injectPathUrl(data, "locations")

  if (hasBuiltInData) {
    return (
      <LocationsPageClient
        data={dataWithUrl}
        navigation={navigation}
        footer={footer}
        enquireFormProperties={enquireFormProperties}
        siteName={siteName}
        schema={SITE_SCHEMA}
      />
    )
  } else {
    // Type-safe checks instead of non-null assertions
    if (!allLocations || !allMentors || !initialLocationsData) {
      throw new Error("Required data not available")
    }

    const transformedLocations = transformLocationsToMapLocations(allLocations)
    const serializedInitialLocations = JSON.stringify(initialLocationsData)

    const locationCount = initialLocationsData._allLocationsMeta?.count || 0
    const mentorCount = initialLocationsData._allMentorsMeta?.count || 0
    const totalLocations = Math.max(locationCount, mentorCount)
    const totalPages = Math.ceil(totalLocations / 12)

    // Transform mentors for map display
    const transformedMentors = allMentors.map((mentor) => ({
      id: mentor.id,
      location: mentor.location,
      coursesOffered: mentor.qualifications ? mentor.qualifications.map((q) => q.slug) : [],
      state: (mentor as any)?.state as InteractiveMap.State,
      recordType: "Mentor" as const,
      isOnCampus: false,
      isWithAIPTMentor: true,
    }))

    return (
      <LocationsPageClient
        data={dataWithUrl}
        navigation={navigation}
        footer={footer}
        enquireFormProperties={enquireFormProperties}
        locations={transformedLocations}
        mentors={transformedMentors}
        initialLocations={serializedInitialLocations}
        siteName={siteName}
        schema={SITE_SCHEMA}
        currentPage={parsedParams.page}
        totalPages={totalPages}
        totalLocations={totalLocations}
        initialSearchParams={parsedParams}
      />
    )
  }
}

import path from "node:path"
import { fileURLToPath } from "node:url"
import { defineConfig } from "vitest/config"

const dirname = typeof __dirname !== "undefined" ? __dirname : path.dirname(fileURLToPath(import.meta.url))

// Simple configuration to test that imported stories work with React 19
export default defineConfig({
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: [".storybook/vitest.setup.ts"],
    include: ["modules/apricot/stories/**/*.test.{ts,tsx}"],
  },
  // Add React 19 specific configuration
  resolve: {
    alias: {
      // Use React 19 compatible shim
      "@storybook/react-dom-shim": "@storybook/react-dom-shim/dist/react-19",
      // Add module aliases
      "@module/apricot": path.resolve(dirname, "modules/apricot"),
    },
  },
})

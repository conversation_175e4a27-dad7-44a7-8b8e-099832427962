import { Provider } from "react-redux"
import { SectionWrapper } from "@module/apricot/components/section-wrapper"
import { Heading } from "@module/apricot/components/heading"

import { getArticlesPerPage } from "@cms/article-landing"
import getGlobalFooter from "@cms/global-footer"
import getGlobalNavigation from "@cms/global-navigation"
import getEnquireForm from "@cms/global-enquire-form"

import configureArticleStore from "@module/article-landing/controller/store"
import { ArticleCards, ArticlePagination, CategoryTagsFilter } from "@module/article-landing/ui"
import { Footer } from "@module/footer"
import { GlobalNavigation } from "@module/global-navigation"
import { EnquireFormProvider } from "@module/enquire-form/service/EnquireFormContext"

import { siteName } from "apricot.config"

import { Seo } from "@module/seo/ui"
import { injectPathUrl } from "@module/seo/service/inject-path-url"
import { appendWithDecoratedDot } from "@module/utilities"
import { SITE_SCHEMA } from "@module/utilities/seo-schema"
import { useRouter } from "next/router"
import type { GetStaticPaths, GetStaticProps } from "next"

type ArticlesPageProps = {
  data: {
    listingTitle: string
    allBlogCategories: { id: string; title: string; slug: string }[]
    _allArticlesMeta: { count: number }
    allArticles: any[]
    seo: any
  }
  navigation: any
  footer: any
  enquireFormProperties: any
  page: number
  category: string
}

export default function ArticlesPageWithPagination({
  data,
  navigation,
  footer,
  enquireFormProperties,
  page,
  category,
}: ArticlesPageProps) {
  const router = useRouter()

  if (router.isFallback) {
    return <div>Loading...</div>
  }

  return (
    <EnquireFormProvider properties={enquireFormProperties}>
      <Seo {...data.seo} schema={SITE_SCHEMA} title={`Articles - Page ${page} - ${data.seo.title}`} />
      <GlobalNavigation {...navigation} />
      <Provider
        store={configureArticleStore({
          filter: {
            availableCategories: data.allBlogCategories,
            selectedCategory: category,
          },
          pagination: {
            perPage: 12,
            currentPage: page - 1,
            totalRecords: data._allArticlesMeta.count,
          },
        })}
      >
        <SectionWrapper bg="neutral">
          <Heading as="h2" type="h2" css={{ textAlign: "center", px: "$base" }}>
            {appendWithDecoratedDot(data.listingTitle)}
          </Heading>
          <CategoryTagsFilter />
          <ArticleCards
            articles={{
              allArticles: data.allArticles,
              _allArticlesMeta: data._allArticlesMeta,
            }}
          />
          <ArticlePagination />
        </SectionWrapper>
      </Provider>
      <Footer {...footer} reversed siteName={siteName} />
    </EnquireFormProvider>
  )
}

export const getStaticProps: GetStaticProps = async ({ params }) => {
  if (!params?.page) {
    throw new Error("Page number is required")
  }

  const pageParam = params.page as string
  const page = parseInt(pageParam, 10)

  if (isNaN(page) || page < 1) {
    return { notFound: true }
  }

  const category = ""

  const [{ data }, navigation, footer, enquireFormProperties] = await Promise.all([
    getArticlesPerPage(page - 1, 12, category),
    getGlobalNavigation(),
    getGlobalFooter(),
    getEnquireForm(),
  ])

  const totalPages = Math.ceil(data._allArticlesMeta.count / 12)
  if (page > totalPages && totalPages > 0) {
    return { notFound: true }
  }

  return {
    props: {
      data: injectPathUrl(data, `articles/page/${page}`),
      navigation,
      footer,
      enquireFormProperties,
      page,
      category,
    },
    revalidate: 120,
  }
}

export const getStaticPaths: GetStaticPaths = async () => {
  const initialResponse = await getArticlesPerPage(0, 12, "")
  const totalPages = Math.ceil(initialResponse.data._allArticlesMeta.count / 12)
  const pagesToGenerate = Math.min(totalPages, 5)

  const paths = []
  for (let page = 2; page <= pagesToGenerate; page++) {
    paths.push({
      params: { page: page.toString() },
    })
  }

  return {
    paths,
    fallback: true,
  }
}

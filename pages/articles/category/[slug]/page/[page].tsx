import { Provider } from "react-redux"
import { SectionWrapper } from "@module/apricot/components/section-wrapper"
import { Heading } from "@module/apricot/components/heading"

import { getArticlesPerPage } from "@cms/article-landing"
import getGlobalFooter from "@cms/global-footer"
import getGlobalNavigation from "@cms/global-navigation"
import getEnquireForm from "@cms/global-enquire-form"

import configureArticleStore from "@module/article-landing/controller/store"
import { ArticleCards, ArticlePagination, CategoryTagsFilter } from "@module/article-landing/ui"
import { Footer } from "@module/footer"
import { GlobalNavigation } from "@module/global-navigation"
import { EnquireFormProvider } from "@module/enquire-form/service/EnquireFormContext"

import { siteName } from "apricot.config"

import { Seo } from "@module/seo/ui"
import { injectPathUrl } from "@module/seo/service/inject-path-url"
import { appendWithDecoratedDot } from "@module/utilities"
import { SITE_SCHEMA } from "@module/utilities/seo-schema"
import { useRouter } from "next/router"
import type { GetStaticPaths, GetStaticProps } from "next"

type CategoryPageProps = {
  data: any
  navigation: any
  footer: any
  enquireFormProperties: any
  initialPage: number
  initialCategory: string
  slug: string
}

export default function ArticleCategoryPageWithPagination({
  data,
  navigation,
  footer,
  enquireFormProperties,
  initialPage,
  initialCategory,
  slug,
}: CategoryPageProps) {
  const router = useRouter()

  if (router.isFallback) {
    return <div>Loading...</div>
  }

  const page = initialPage
  const category = initialCategory

  const categoryTitle = data.allBlogCategories.find((cat: any) => cat.slug === slug)?.title || "Category"

  return (
    <EnquireFormProvider properties={enquireFormProperties}>
      <Seo {...data.seo} schema={SITE_SCHEMA} title={`${categoryTitle} Articles - Page ${page} - ${data.seo.title}`} />
      <GlobalNavigation {...navigation} />
      <Provider
        store={configureArticleStore({
          filter: {
            availableCategories: data.allBlogCategories,
            selectedCategory: category,
          },
          pagination: {
            perPage: 12,
            currentPage: page - 1,
            totalRecords: data._allArticlesMeta.count,
          },
        })}
      >
        <SectionWrapper bg="neutral">
          <Heading as="h2" type="h2" css={{ textAlign: "center", px: "$base" }}>
            {appendWithDecoratedDot(`${categoryTitle} Articles`)}
          </Heading>
          <CategoryTagsFilter />
          <ArticleCards
            articles={{
              allArticles: data.allArticles,
              _allArticlesMeta: data._allArticlesMeta,
            }}
          />
          <ArticlePagination />
        </SectionWrapper>
      </Provider>
      <Footer {...footer} reversed siteName={siteName} />
    </EnquireFormProvider>
  )
}

export const getStaticProps: GetStaticProps = async ({ params }) => {
  if (!params?.slug || !params?.page) {
    throw new Error("Slug and page are required for category pagination pages")
  }

  const slug = params.slug as string
  const pageParam = params.page as string
  const initialPage = parseInt(pageParam, 10)

  if (isNaN(initialPage) || initialPage < 1) {
    return {
      notFound: true,
    }
  }

  const initialResponse = await getArticlesPerPage(0, 1, "")
  const categories = initialResponse.data.allBlogCategories

  const category = categories.find((cat) => cat.slug === slug)?.id || ""

  if (!category) {
    return {
      notFound: true,
    }
  }

  const [{ data }, navigation, footer, enquireFormProperties] = await Promise.all([
    getArticlesPerPage(initialPage - 1, 12, category),
    getGlobalNavigation(),
    getGlobalFooter(),
    getEnquireForm(),
  ])

  const totalPages = Math.ceil(data._allArticlesMeta.count / 12)
  if (initialPage > totalPages && totalPages > 0) {
    return {
      notFound: true,
    }
  }

  return {
    props: {
      data: injectPathUrl(data, `articles/category/${slug}/page/${initialPage}`),
      navigation,
      footer,
      enquireFormProperties,
      initialPage,
      initialCategory: category,
      slug,
    },
    revalidate: 120,
  }
}

export const getStaticPaths: GetStaticPaths = async () => {
  const initialResponse = await getArticlesPerPage(0, 1, "")
  const categories = initialResponse.data.allBlogCategories

  const paths: { params: { slug: string; page: string } }[] = []

  for (const category of categories) {
    const categoryResponse = await getArticlesPerPage(0, 12, category.id)
    const totalPages = Math.ceil(categoryResponse.data._allArticlesMeta.count / 12)
    const pagesToGenerate = Math.min(totalPages, 3)

    for (let page = 2; page <= pagesToGenerate; page++) {
      paths.push({
        params: { slug: category.slug, page: page.toString() },
      })
    }
  }

  return {
    paths,
    fallback: true,
  }
}

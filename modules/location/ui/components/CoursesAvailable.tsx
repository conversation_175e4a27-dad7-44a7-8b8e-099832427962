import React from "react"
import { Text } from "@module/apricot/components/text"
import { PillGroup, Pill } from "@module/pill"
import { css } from "@module/apricot/theme/stitches"

interface GenericList {
  id: string
  title: string
}

interface CoursesAvailableProps {
  coursesOffered?: string[]
  availableCourses?: GenericList[]
}

const coursesAvailableStyles = css({
  textAlign: "center",
  width: "100%",
  mt: "$sm",

  "& > strong": {
    display: "block",
    mb: "$xxsm",
    fontSize: "$sm",
    color: "$textHeading",
  },
})()

export const CoursesAvailable = ({ coursesOffered = [], availableCourses = [] }: CoursesAvailableProps) => {
  if (coursesOffered.length === 0 || availableCourses.length === 0) {
    return null
  }

  const pills = availableCourses.filter((c) => coursesOffered.includes(c.id))

  if (pills.length === 0) {
    return null
  }

  const toPill = ({ id, title }: GenericList) => <Pill key={id}>{title}</Pill>

  return (
    <div className={coursesAvailableStyles}>
      <Text as="strong">Courses Available</Text>
      <PillGroup>{pills.map(toPill)}</PillGroup>
    </div>
  )
}

import React from "react"
import { CategoryPills } from "@module/article"
import { useArticleSelector } from "../service/useArticleState"
import { css } from "@module/apricot/theme/stitches"
import { Grid } from "@module/apricot/components/grid"
import { Column } from "@module/apricot/components/grid"

const className = css({
  my: "$md",
  mx: "auto",
  paddingX: "$base",
  "@lg": {
    maxWidth: "1000px",
  },
  display: "flex",
  flexDirection: "row",
  width: "100%",
  alignItems: "center",
  justifyContent: "center",
  flexWrap: "wrap",
})()

export const CategoryTagsFilter = () => {
  const { availableCategories, selectedCategory } = useArticleSelector((state) => state.filter)

  const activeSlug = (() => {
    const activeCategory = availableCategories.find((category) => category.id === selectedCategory)
    return activeCategory?.slug || ""
  })()

  return (
    <Grid>
      <Column col={12} className={className}>
        <CategoryPills categories={availableCategories} activeSlug={activeSlug} wrap={true} />
      </Column>
    </Grid>
  )
}

import { styled } from "@module/apricot/theme/stitches"

// @ts-expect-error Ignore persistent type conflict with Stitches + React 19
export const PaginationLink = styled("a", {
  lineHeight: "30px",
  br: "$sm",
  p: 0,
  px: "$xsm",
  border: "none",
  fontFamily: "$heading",
  fontWeight: 700,
  fontSize: "$xsm",
  background: "transparent",
  color: "$textHeading",
  cursor: "pointer",
  textDecoration: "none",
  display: "inline-block",
  textAlign: "center",

  "&:hover": {
    background: "white",
  },
  variants: {
    active: {
      true: {
        background: "$core3",
        color: "white",
        "&:hover": {
          background: "black",
        },
      },
    },
    iconOnly: {
      true: {
        px: 0,
        width: "30px",
        height: "30px",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      },
    },
    disabled: {
      true: {
        pointerEvents: "none",
        opacity: 0.5,
      },
    },
  },
})

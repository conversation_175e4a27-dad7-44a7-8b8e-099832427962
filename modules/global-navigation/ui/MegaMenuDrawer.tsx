"use client"

import React, { useContext, useCallback, useEffect } from "react"
import { useMediaQuery } from "@mantine/hooks"
import { Column, Grid } from "@module/apricot/components/grid"
import { Box } from "@module/apricot/components/box"
import { usePathname } from "next/navigation"
import { useCSS } from "@module/apricot/theme/provider"
import { useDisclosure } from "@module/apricot/hooks/use-disclosure"

import { MegaMenuContext } from "./MegaMenuContext"
import { MenuMenuBody } from "./MegaMenu"
import { drawer, drawerOverlay } from "../style/index.css"

export const MegaMenuDrawer = () => {
  const pathname = usePathname()
  const { record, setRecord } = useContext(MegaMenuContext)
  const isMobile = useMediaQuery("(max-width: 1199px)")

  const drawerClassname = drawer({
    placement: "top",
  })
  const drawerOverlayClassname = drawerOverlay()
  const drawerBodyClassname = useCSS("Drawer.Body")()

  const open = typeof record !== "undefined"

  const onClose = useCallback(() => {
    setRecord(undefined)
  }, [setRecord])

  useEffect(() => {
    if (isMobile && open) {
      onClose()
    }
  }, [isMobile, open, onClose])

  // Close menu when pathname changes (route navigation)
  useEffect(() => {
    onClose()
  }, [pathname, onClose])

  const NAVIGATION_HEIGHT = "115px"

  return (
    <>
      <Box
        className={drawerOverlayClassname}
        onClick={onClose}
        style={{
          position: "fixed",
          top: NAVIGATION_HEIGHT,
          left: 0,
          width: "100%",
          height: `calc(100% - ${NAVIGATION_HEIGHT})`,
          zIndex: 200,
          opacity: open ? 0.48 : 0,
          pointerEvents: open ? "auto" : "none",
          transition: "opacity 0.3s ease",
          visibility: open ? "visible" : "hidden",
        }}
      />

      <Box
        className={drawerClassname}
        style={{
          position: "absolute",
          top: NAVIGATION_HEIGHT,
          left: 0,
          width: "100%",
          zIndex: 200,
          height: "auto",
          display: "flex",
          flexDirection: "column",
          background: "#F5F5F5",
          paddingTop: "32px",
          paddingBottom: "64px",
          transform: open ? "translateY(0)" : "translateY(-100%)",
          transition: "transform 0.3s ease",
          visibility: open ? "visible" : "hidden", // Hide from accessibility tree when closed
        }}
      >
        <Box className={drawerBodyClassname}>
          <Grid
            css={{
              m: 0,
              p: 0,
              width: "100%",
              paddingLeft: "24px",
              paddingRight: "24px",
              margin: "0 auto",
              maxWidth: "var(--sizes-xl)",
            }}
          >
            <Column>
              {/* We render the current dropdown menu content */}
              <div style={{ display: "contents" }}>
                {record && (
                  <MenuMenuBody
                    dropdownTitle={record.dropdownTitle}
                    linkColumns={record.linkColumns}
                    link={record.link}
                  />
                )}
              </div>
            </Column>
          </Grid>
        </Box>
      </Box>
    </>
  )
}

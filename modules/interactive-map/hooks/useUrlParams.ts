"use client"

import { useEffect } from "react"
import { usePathname, useSearchParams } from "next/navigation"
import { useMapSelector } from "../service/useMapState"

export function useUrlParams() {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const { course, state, isOnCampus, isWithAIPTMentor, includeSurroundingArea } = useMapSelector(
    (state) => state.filter,
  )
  const { currentPage } = useMapSelector((state) => state.pagination)

  useEffect(() => {
    if (!searchParams) return

    // Handle URL updates for page 1 only (pagination handles page 2+)
    const isPage1 = currentPage === 0

    // Only handle URL updates when on page 1
    // This includes both filter changes and pagination resets
    if (!isPage1) {
      return
    }

    const params = new URLSearchParams(searchParams.toString())

    // Note: View mode is managed purely client-side, not synced to URL

    // Update course parameter
    if (course) {
      params.set("course", course)
    } else {
      params.delete("course")
    }

    // Update state parameter
    if (state) {
      params.set("state", state)
    } else {
      params.delete("state")
    }

    // Update delivery type parameters
    if (!isOnCampus) {
      params.set("isOnCampus", "false")
    } else {
      params.delete("isOnCampus")
    }

    if (!isWithAIPTMentor) {
      params.set("isWithAIPTMentor", "false")
    } else {
      params.delete("isWithAIPTMentor")
    }

    // Update surrounding area parameter
    if (includeSurroundingArea) {
      params.set("includeSurroundingArea", "true")
    } else {
      params.delete("includeSurroundingArea")
    }

    // For page 1, always use base path (not pagination path)
    const basePath = pathname?.replace(/\/pages\/\d+$/, "") || "/locations"
    const newUrl = `${basePath}?${params.toString()}`.replace(/\?$/, "")
    const currentFullUrl = window.location.pathname + window.location.search

    // Only update URL if it has actually changed
    if (newUrl !== currentFullUrl) {
      window.history.replaceState(null, "", newUrl)
    }
  }, [course, state, isOnCampus, isWithAIPTMentor, includeSurroundingArea, currentPage, pathname, searchParams])
}

"use client"

import { useEffect } from "react"
import { useMapDispatch } from "../service/useMapState"
import { setViewMode } from "../controller/slice/filterDataSlice"
import useLocalStorage from "@module/storage/service/useLocalStorage"
import { KEY_VIEW_MODE_STATE } from "../common/constants"
import { InteractiveMap } from "../common/types"

export function ViewModeSync() {
  const dispatch = useMapDispatch()
  const { storageValue: storedViewMode } = useLocalStorage<InteractiveMap.ViewMode>(KEY_VIEW_MODE_STATE)

  useEffect(() => {
    if (storedViewMode) {
      dispatch(setViewMode(storedViewMode))
    }
  }, [storedViewMode, dispatch])

  return null
}

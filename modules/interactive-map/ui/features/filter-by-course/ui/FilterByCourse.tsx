"use client"

import React from "react"
import { useMapDispatch, useMapSelector } from "@module/interactive-map/service/useMapState"
import { setCourse } from "@module/interactive-map/controller/slice/filterDataSlice"

export const FilterByCourse = () => {
  const { availableCourses, course } = useMapSelector((state) => state.filter)
  const dispatch = useMapDispatch()

  const onChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(setCourse(e.target.value))
  }

  const toOption = ({ title, id }: { title: string; id: string }) => (
    <option key={id} value={id}>
      {title}
    </option>
  )

  return (
    <select onChange={onChange} value={course}>
      <option value="">All Courses</option>
      {availableCourses.map(toOption)}
    </select>
  )
}

"use client"

import React, { memo } from "react"

import { useViewMode } from "@module/interactive-map/service/useViewMode"
import { useHasSearchBeenPerformed } from "@module/interactive-map/service/useHasSearchBeenPerformed"

import { ListPagination } from "./ListPagination"

import { SecondaryFilter } from "../../secondary-filter/ui"
import { ViewWrapper } from "../../view-wrapper/ui"
import { PostSearchView } from "./PostSearchView"
import { PreSearchView } from "./PreSearchView"

interface ListViewProps {
  initialData?: any
}

export const ListView = memo(({ initialData }: ListViewProps) => {
  const { isListView } = useViewMode()
  const hasSearched = useHasSearchBeenPerformed()

  return (
    <ViewWrapper isListView={isListView}>
      <SecondaryFilter />
      {hasSearched && initialData === undefined ? <PostSearchView /> : <PreSearchView initialData={initialData} />}
      <ListPagination />
    </ViewWrapper>
  )
})

ListView.displayName = "ListView"

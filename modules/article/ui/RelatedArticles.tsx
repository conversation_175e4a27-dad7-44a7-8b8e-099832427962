import React from "react"
import { Grid, Column } from "@module/apricot/components/grid"
import { Heading } from "@module/apricot/components/heading"
import { ArticleCard } from "./ArticleCard"
import { css } from "@module/apricot/theme/stitches"
import { richText, wrapper } from "@module/article-content/style/index.css"
import { RichText as RichTextWrapper } from "@module/apricot/components/rich-text"

import type { ArticleCardProperties } from "../common/types"
import { SectionWrapper } from "@module/apricot/components/section-wrapper"

const articleCardsContainer = css({
  display: "grid",
  gridTemplateColumns: "1fr",
  gap: "$base",
  alignItems: "stretch",
  "@md": {
    gridTemplateColumns: "1fr 1fr",
  },
  "> div": {
    "@md": {
      flex: "0 0 calc(50% - $space$sm)",
    },
  },
  "> div h3": {
    fontSize: "$fontSizes$xmd",
  },
  "> a": {
    textDecoration: "none !important",
  },
  "> a > div": {
    height: "100%",
  },
  "> a:hover": {
    textDecoration: "none !important",
    color: "$primary",
  },
  "> a:hover h3": {
    textDecoration: "underline !important",
  },
  "> a p": {
    display: "none",
  },
})()

interface RelatedArticlesProps {
  articles: (ArticleCardProperties & { id: string })[]
  currentArticleSlug: string
}

export const RelatedArticles = ({ articles, currentArticleSlug }: RelatedArticlesProps) => {
  const filteredArticles = articles.filter((article) => article.slug !== currentArticleSlug).slice(0, 2)

  if (filteredArticles.length === 0) {
    return null
  }

  return (
    <SectionWrapper bg="neutral" className={wrapper}>
      <Grid>
        <Column col={12}>
          <RichTextWrapper className={richText}>
            <Heading as="h2">Related Articles</Heading>
            <div className={articleCardsContainer}>
              {filteredArticles.map(({ id, ...article }) => (
                <ArticleCard {...article} key={id} headingLevel="h3" />
              ))}
            </div>
          </RichTextWrapper>
        </Column>
      </Grid>
    </SectionWrapper>
  )
}

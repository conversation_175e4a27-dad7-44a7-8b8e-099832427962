import { REGEX } from "@module/enquire-form/common/constants"

export const collect = <T extends any[], <PERSON> extends keyof T[0]>(target: T, key: K): T[0][K][] => {
  return target.map((value) => value[key])
}

export const undefinedArrayHasItems = <T>(array: T[] | undefined): array is T[] => {
  return typeof array !== "undefined" && array.length > 0
}

export const parseWindowCookies = async () => {
  if (typeof document === "undefined") return {}
  const { parse } = await import("cookie")
  return parse(document.cookie)
}

export const appendWithDecoratedDot = (value: string): string => {
  return value.replace(/\.(?=\*$|$)/g, '<span class="decorated-dot">.</span>')
}

export const formatCurrentUrl = (url: string): string[] => {
  const urlPattern = /\/courses(\/[^#]+)#enquire-now/
  const cleanUrl = url
    .replace(urlPattern, "$1")
    .split("/")
    .filter((segment) => segment !== "")
  return cleanUrl
}

export const formatCoursesToUrl = (courseName: string): string => {
  return courseName.replace(/[()]/g, "").toLowerCase().replace(/\s/g, "-").replace("+", "and")
}

export const validateEmail = (email: string): boolean => {
  const emailPattern = REGEX.EMAIL
  return !!email.match(emailPattern)
}

export const validatePhoneNumber = (phone: string): boolean => {
  const phonePattern = REGEX.PHONE
  return phone.match(phonePattern) !== null
}

export const validatePostCode = (postCode: string): boolean => {
  const postCodePattern = REGEX.POSTCODE
  return postCode.match(postCodePattern) !== null
}

export const safeJsonParse = <T = any>(jsonString: string | null | undefined, fallback: T | null = null): T | null => {
  if (!jsonString || typeof jsonString !== "string") return fallback
  try {
    return JSON.parse(jsonString) as T
  } catch (error) {
    console.error("Failed to parse JSON:", error)
    return fallback
  }
}

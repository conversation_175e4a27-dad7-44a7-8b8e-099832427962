import { it, expect, describe, vi } from "vitest"

// Mock all modules before importing story
vi.mock("../../components/button", () => ({
  Button: vi.fn(() => null),
}))

// Import after mocking
import { Primary } from "./Button.stories"

// Basic smoke test for Button component with React 19
describe("Button Component", () => {
  it("should have a Primary story", () => {
    expect(Primary).toBeDefined()
    expect(Primary.args).toHaveProperty("variant", "primary")
    expect(Primary.args).toHaveProperty("label", "Button")
  })
})

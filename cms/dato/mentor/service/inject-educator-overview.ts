import { gql } from "@apollo/client"
import { collectFragments, composeFragments } from "@module/apricot/utils/fragment"

import CMS from "@cms/core"

import type { TransformedData } from "@cms/types/mentor"
import { EducatorOverviewStruct } from "@module/educator-overview/common/types"
import { RichTextIntegration } from "@module/rich-text/service"
import { ExtendedSmartLink } from "@module/smart-link"

const MENTOR_OVERVIEW_QUERY = gql`
  ${ExtendedSmartLink.fragment}

  query FetchMentorOverview($id: ItemId!) {
    mentor(filter: { id: { eq: $id } }) {
      id
      bio {
        value
        links {
          __typename
          ...on ArticleRecord {
            id
            slug
            title
          }
          ...on PageRecord {
            id 
            slug
            title
            promoteAsHomepage
          }
          ...on CourseRecord {
            id
            slug
            title
          }
          ...on LocationsLandingRecord {
            id
          }
          ...on MentorRecord {
            id
            slug
            name
          }
          ...on LocationRecord {
            id
            slug
            title
          }
          ...on ContentSmartLinkRecord {
            ...${ExtendedSmartLink.fragmentName}
          }
        }
        blocks
      }
      qualifications {
        id
        title
      }
      teachesAt {
        id
        title
        locationAddress
        slug
      }
      detailsTitle
      qualificationsTitle
      campusTitle
      contactAction {
        ...${ExtendedSmartLink.fragmentName}
      }
    }
  }
`

class InjectEducatorOverview {
  private flexibleContent: TransformedData[]

  private mentorId: string

  public constructor(flexibleContent: TransformedData[], mentorId: string) {
    this.flexibleContent = flexibleContent
    this.mentorId = mentorId
    return this
  }

  private executeQuery() {
    return CMS.client.query<{ mentor: EducatorOverviewStruct.InjectDatoResponse }>({
      query: MENTOR_OVERVIEW_QUERY,
      variables: {
        id: this.mentorId,
      },
      fetchPolicy: CMS.fetchPolicy,
    })
  }

  private injectOverviewData({
    bio: { value: bioValue, links, blocks = [] },
    campusTitle,
    detailsTitle,
    qualifications,
    qualificationsTitle,
    teachesAt,
    contactAction,
  }: EducatorOverviewStruct.InjectDatoResponse): TransformedData[] {
    return this.flexibleContent.map((value) => {
      if (value.__typename === "MentorOverview") {
        return {
          __typename: value.__typename,
          id: value.id,
          campusTitle,
          detailsTitle,
          qualifications,
          qualificationsTitle,
          teachesAt,
          ...(contactAction ? { contactAction: ExtendedSmartLink.transformQueryData(contactAction) } : {}),
          bio: {
            value: bioValue,
            blocks,
            links: RichTextIntegration.transformLinks(links),
          },
        }
      }

      return value
    })
  }

  private containsBlock(): boolean {
    return this.flexibleContent.some(({ __typename }) => __typename === "MentorOverview")
  }

  public async execute() {
    if (!this.containsBlock()) return this.flexibleContent

    const overviewResponse = await this.executeQuery()

    return this.injectOverviewData(overviewResponse.data.mentor)
  }
}

export default InjectEducatorOverview

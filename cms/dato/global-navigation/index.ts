import { gql } from "@apollo/client"
import { collectFragments, composeFragments } from "@module/apricot/utils/fragment"
import { DatoGlobalNavigationInstance } from "@module/global-navigation"

import CMS from "../core"

import type { GlobalNavigationResponse } from "@module/global-navigation/common/types"
import type { SmartLinkIntegration } from "@module/apricot/integration/smart-link"

const NAVIGATION_QUERY = gql`
${composeFragments(collectFragments([DatoGlobalNavigationInstance]))}

query SiteNavigation {
  globalNavigation {
    id
    ...${DatoGlobalNavigationInstance.fragmentName}
  }
}
`

type GraphQLResponse = {
  globalNavigation: GlobalNavigationResponse<SmartLinkIntegration.DatoResponse>
}

const getGlobalNavigation = async () => {
  const { data } = await CMS.client.query<GraphQLResponse>({
    query: NAVIGATION_QUERY,
    fetchPolicy: CMS.fetchPolicy,
  })

  return DatoGlobalNavigationInstance.transformQueryData(data.globalNavigation)
}

export default getGlobalNavigation

import { gql } from "@apollo/client"
import { collectFragments, composeFragments } from "@module/apricot/utils/fragment"
import { composeComponentsForQuery } from "@module/apricot/utils/compose-components"

import { articleLandingBannerComponents } from "@cms/config/components"
import { ArticleCardIntegration } from "@module/article"
import { SeoIntegration } from "@module/seo/service"

const mergedComponents = [...articleLandingBannerComponents, ArticleCardIntegration, SeoIntegration]

export const getArticlesPerPageQuery = (currentPage: number = 0, perPage: number = 12, category: string = "") => {
  return gql`
    ${composeFragments(collectFragments(mergedComponents))}

    query ArticleLanding {
      articleLanding {
        banner {
          __typename
          ${composeComponentsForQuery(articleLandingBannerComponents)}
        }
        listingTitle
        seo {
          ...${SeoIntegration.fragmentName}
        }
      }
      allArticles(
        first: ${perPage}
        skip: ${currentPage * perPage}
        filter: { _status: { eq: published }, categories: { anyIn: ${JSON.stringify(category ? [category] : [])} } }
        orderBy: publishDate_DESC
      ) {
        id
        ...${ArticleCardIntegration.fragmentName}
      }
      _allArticlesMeta(filter: { _status: { eq: published }, categories: { anyIn: ${JSON.stringify(
        category ? [category] : [],
      )} } }) {
        count
      }
      allBlogCategories(filter: {_status: {eq: published}}) {
        id
        title
        slug
      }
    }
  `
}

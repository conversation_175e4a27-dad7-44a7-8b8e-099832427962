import transformMultiReferenceRecords from "@module/apricot/utils/transform-multi-reference-records"
import cms from "@cms/core/cms"

import { articleLandingBannerComponents } from "@cms/config/components"
import { ArticleCardIntegration } from "@module/article"
import { SeoIntegration } from "@module/seo/service"

import { getArticlesPerPageQuery } from "./query"

import type { PageDataReturn, QueryReturn, TransformedData } from "@cms/types/article-landing"

export async function getArticlesPerPage(
  currentPage: number = 0,
  perPage: number = 12,
  category: string = "",
): Promise<PageDataReturn> {
  const { data } = await cms.client.query<QueryReturn>({
    query: getArticlesPerPageQuery(currentPage, perPage, category),
    fetchPolicy: cms.fetchPolicy,
  })

  const { banner, listingTitle, seo } = data.articleLanding
  const { allBlogCategories, allArticles, _allArticlesMeta } = data

  return {
    data: {
      banner: transformMultiReferenceRecords<TransformedData>([banner], articleLandingBannerComponents),
      listingTitle,
      allBlogCategories,
      _allArticlesMeta,
      allArticles: allArticles.map((value) => ({
        id: value.id,
        ...ArticleCardIntegration.transformQueryData(value),
      })),
      seo: SeoIntegration.transformQueryData(seo),
    },
  }
}

import { DatoBannerFiftyFifty } from "@module/apricot/integration/banner/fifty-fifty"
import { DatoBannerHeroInstance } from "@module/banner-hero"
import { DatoImageCardSet } from "@module/apricot/integration/image-card-set"
import { DatoIconCardSet } from "@module/apricot/integration/icon-card-set"
import { ExtendedCallToAction } from "@module/call-to-action"
import { DatoBannerLanding } from "@module/apricot/integration/banner/landing"
import { DatoBannerFeatureInstance } from "@module/banner-feature"
import { DatoPromotionalBanner } from "@module/banner-promotional"
import { ExtendedSmartLink } from "@module/smart-link"
import { ArticleCardSetIntegration } from "@module/article-card-set"
import { DatoImageCard } from "@module/apricot/integration/image-card"
import { DatoIconCard } from "@module/apricot/integration/icon-card"
import { CourseCardSetIntegration } from "@module/course-card-set"
import { DatoImageGalleryInstance } from "@module/apricot/integration/image-gallery"
import { DatoTestimonialSetInstance } from "@module/apricot/integration/testimonial-set"
import { MentorCardSetIntegration } from "@module/mentor-card-set"
import { DynamicMentorIntegration } from "@module/mentor"
import { DiverseRecordCardSetIntegration } from "@module/diverse-card-set/service"
import { DynamicLocationCourseCollectionIntegration } from "@module/course"
import { CourseFeedIntegration } from "@module/course-feed/service"
import { UnitTabSetIntegration } from "@module/unit-tab-set/service"
import { BannerCallToActionIntegration } from "@module/banner-call-to-action/service"
import { SplitIconCardSetIntegration } from "@module/split-icon-cards/service"
import { DynamicLocationOverviewIntegration } from "@module/location/service"
import { IntakeCallToActionIntegration } from "@module/location/service"
import { DynamicEducatorOverviewIntegration } from "@module/educator-overview/service"
import { RichTextIntegration } from "@module/rich-text/service"
import { SocialProofIntegration } from "@module/social-proof/service"
import { EnquireFormIntegration } from "@module/enquire-form/service"
import MapCallToActionIntegration from "@module/interactive-map/service/map-call-to-action-integration"
import { ProductCardSetIntegration } from "@module/commerce/service"
import { DatoPromotionalImageBanner } from "@module/banner-promotional-image"
import { DatoPromotionalFiftyFiftyBanner } from "@module/banner-promotional-fifty-fifty"
import ReferralFormIntegration from "@module/referral-form/service"
import { DatoContentHtml } from "@module/apricot/integration/contentHtml"
import { DatoContentLogoScroller } from "@module/apricot/integration/content-logo-scroller"
import { DatoContentIconBannerSet } from "@module/apricot/integration/content-icon-banner-set"

const DatoImageCardSetInstance = new DatoImageCardSet(ExtendedSmartLink, new DatoImageCard(ExtendedSmartLink))
const DatoIconCardSetInstance = new DatoIconCardSet(ExtendedSmartLink, new DatoIconCard(ExtendedSmartLink))
const DatoFiftyFiftyInstance = new DatoBannerFiftyFifty(ExtendedSmartLink)
const DatoLandingInstance = new DatoBannerLanding(ExtendedSmartLink)
const DatoContentHtmlInstance = new DatoContentHtml()
const DatoContentLogoScrollerInstance = new DatoContentLogoScroller()
const DatoContentIconBannerSetInstance = new DatoContentIconBannerSet()

const bannerComponents = [
  DatoFiftyFiftyInstance,
  DatoBannerHeroInstance,
  DatoLandingInstance,
  DatoBannerFeatureInstance,
]

const globalComponents = [
  DatoImageCardSetInstance,
  DatoIconCardSetInstance,
  ExtendedCallToAction,
  ArticleCardSetIntegration,
  CourseCardSetIntegration,
  DatoImageGalleryInstance,
  DatoTestimonialSetInstance,
  DiverseRecordCardSetIntegration,
  BannerCallToActionIntegration,
  DatoPromotionalBanner,
  SocialProofIntegration,
  EnquireFormIntegration,
  MapCallToActionIntegration,
  ProductCardSetIntegration,
  DatoPromotionalImageBanner,
  DatoPromotionalFiftyFiftyBanner,
]

export const pageComponents = [
  ...globalComponents,
  ...bannerComponents,
  MentorCardSetIntegration,
  CourseFeedIntegration,
  RichTextIntegration,
  ReferralFormIntegration,
  DatoContentHtmlInstance,
  DatoContentLogoScrollerInstance,
  DatoContentIconBannerSetInstance,
]

export const homepageComponents = pageComponents

export const locationPageComponents = [
  ...globalComponents,
  ...bannerComponents,
  DynamicMentorIntegration,
  DynamicLocationCourseCollectionIntegration,
  DynamicLocationOverviewIntegration,
  IntakeCallToActionIntegration,
]

export const mentorPageComponents = [...globalComponents, ...bannerComponents, DynamicEducatorOverviewIntegration]

export const articlePageComponents = [
  DatoImageCardSetInstance,
  DatoIconCardSetInstance,
  ExtendedCallToAction,
  CourseCardSetIntegration,
  DiverseRecordCardSetIntegration,
  MapCallToActionIntegration,
  MentorCardSetIntegration,
  DatoTestimonialSetInstance,
  BannerCallToActionIntegration,
  SocialProofIntegration,
  EnquireFormIntegration,
  ProductCardSetIntegration,
]

export const coursePageComponents = [
  ...globalComponents,
  UnitTabSetIntegration,
  SplitIconCardSetIntegration,
  DatoContentHtmlInstance,
  RichTextIntegration,
]

export const courseBannerComponents = bannerComponents

export const articleLandingBannerComponents = bannerComponents
export const shopLandingBannerComponents = bannerComponents

export const locationLandingBannerComponents = bannerComponents
export const locationLandingComponents = [...globalComponents, ...bannerComponents]

import { gql } from "@apollo/client"
import { collectFragments, composeFragments } from "@module/apricot/utils/fragment"
import { composeComponentsForQuery } from "@module/apricot/utils/compose-components"

import { courseBannerComponents, coursePageComponents } from "../config/components"

import { CourseOverviewStruct, CoursePromoBanner } from "@module/course-overview/common/types"
import { CourseOverviewIntegration } from "@module/course-overview/service"
import { DeliveryStructureIntegration } from "@module/delivery-structure/service"
import { SeoStruct } from "@module/seo/common/types"
import { SeoIntegration } from "@module/seo/service"

import type { GraphQLBannerInputData, GraphQLInputData } from "../types/course"
import { DeliveryStructureStruct } from "@module/delivery-structure/common/types"
import { ExtendedSmartLink } from "@module/smart-link"
import { ExtendedDatoResponse } from "@module/smart-link/common/types"

const mergedComponents = [
  ...coursePageComponents,
  ...courseBannerComponents,
  CourseOverviewIntegration,
  DeliveryStructureIntegration,
  ExtendedSmartLink,
  SeoIntegration,
]

export const COURSE_PAGE_QUERY = gql`
  ${composeFragments(collectFragments(mergedComponents))}

  query Course($slug: String!) {
    course(filter: { slug: { eq: $slug } }) {
      id
      title
      flexibleContent {
        __typename
        ${composeComponentsForQuery(coursePageComponents)}
      }
      introductionBanner {
        __typename
        ${composeComponentsForQuery(courseBannerComponents)}
      }
      inPageNavLinks {
        ...${ExtendedSmartLink.fragmentName}
      }
      seo {
        ...${SeoIntegration.fragmentName}
      }
      ...${CourseOverviewIntegration.fragmentName}
      ...${DeliveryStructureIntegration.fragmentName}
      promoBanner {
        image {
          url
          responsiveImage {
            alt
            height
            width
            src
            aspectRatio
          }
        }
        imageMobile {
          url
          responsiveImage {
            alt
            height
            width
            src
            aspectRatio
          }
        }
        action {
          id
          internalLink {
            ... on PageRecord {
              id
              slug
              title
            }
          }
          externalLink
          id
          linkType
          title
          trackingTitle
        }
      }
    }
  }
`

export const COURSE_SLUGS_QUERY = gql`
  query CoursePageSlugs($skip: IntType!) {
    allCourses(filter: { _status: { eq: published } }, first: "100", skip: $skip) {
      id
      slug
    }
  }
`

export const COURSE_TOTAL_QUERY = gql`
  query CourseTotalCount {
    _allCoursesMeta {
      count
    }
  }
`

export type CoursePageQueryReturn = {
  course: {
    title: string
    flexibleContent: GraphQLInputData[]
    introductionBanner: GraphQLBannerInputData
    inPageNavLinks?: ExtendedDatoResponse[]
    seo: SeoStruct.DatoResponse
    courseOverview: CourseOverviewStruct.DatoResponse
    promoBanner: CoursePromoBanner.DatoResponse
  } & DeliveryStructureStruct.DatoResponse &
    CourseOverviewStruct.DatoResponse
}

export type CoursePageSlugQueryReturn = {
  allCourses: {
    slug: string
  }[]
}

export type TotalRecordQueryReturn = {
  _allCoursesMeta: {
    count: number
  }
}

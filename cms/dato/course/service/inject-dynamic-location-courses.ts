import { gql } from "@apollo/client"

import CMS from "@cms/core"
import { SimpleCourseCardIntegration, IntakeClusterIntegration } from "@module/course"

import type { TransformedData } from "@cms/types/location"
import type { InjectDynamicLocationCourseStruct } from "@module/course/common/types"

const FETCH_COURSES = gql`
  ${IntakeClusterIntegration.fragment}
  ${SimpleCourseCardIntegration.fragment}

  query FetchRelatedDynamicLocationCourses($id: ItemId) {
    location(filter: { id: { eq: $id } }) {
      id
      intakeClusters {
        ...${IntakeClusterIntegration.fragmentName}
      }
      coursesOffered {
        id
        ...${SimpleCourseCardIntegration.fragmentName}
      }
    }
  }
`

class InjectLocationCourses {
  flexibleContent: TransformedData[]

  locationId: string

  public constructor(flexibleContent: TransformedData[], locationId: string) {
    this.flexibleContent = flexibleContent
    this.locationId = locationId
    return this
  }

  private containsBlock(): boolean {
    return this.flexibleContent.some(({ __typename }) => __typename === "DynamicLocationCourseCollection")
  }

  private executeQuery() {
    return CMS.client.query<InjectDynamicLocationCourseStruct.DatoResponse>({
      query: FETCH_COURSES,
      variables: {
        id: this.locationId,
      },
      fetchPolicy: CMS.fetchPolicy,
    })
  }

  private transformCourseData(
    response: InjectDynamicLocationCourseStruct.DatoResponse,
  ): InjectDynamicLocationCourseStruct.TransformedCourses {
    const { coursesOffered, intakeClusters } = response.location

    return coursesOffered.map((course) => ({
      id: course.id,
      ...SimpleCourseCardIntegration.transformQueryData(course),
      intakeClusters: intakeClusters.map((value) => IntakeClusterIntegration.transformQueryData(value)),
    }))
  }

  private injectCourseData(data: InjectDynamicLocationCourseStruct.TransformedCourses): TransformedData[] {
    return this.flexibleContent.map((value) => {
      if (value.__typename === "DynamicLocationCourseCollection") {
        return {
          ...value,
          courses: data,
        }
      }

      return value
    })
  }

  public async execute(): Promise<TransformedData[]> {
    if (!this.containsBlock()) return this.flexibleContent

    const { data } = await this.executeQuery()
    const transformedCourseData = this.transformCourseData(data)
    const injectedCourses = this.injectCourseData(transformedCourseData)

    return injectedCourses
  }
}

export default InjectLocationCourses

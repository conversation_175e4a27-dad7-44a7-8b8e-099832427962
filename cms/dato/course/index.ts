import transformMultiReferenceRecords from "@module/apricot/utils/transform-multi-reference-records"
import CMS from "../core"
import { TransformedData, PageDataReturn, TransformedBannerData } from "../types/course"
import {
  COURSE_PAGE_QUERY,
  CoursePageQueryReturn,
  COURSE_SLUGS_QUERY,
  CoursePageSlugQueryReturn,
  TotalRecordQueryReturn,
  COURSE_TOTAL_QUERY,
} from "./query"
import { courseBannerComponents, coursePageComponents } from "../config/components"
import { CourseOverviewIntegration } from "@module/course-overview/service"
import { DeliveryStructureIntegration } from "@module/delivery-structure/service"
import FetchAllAvailableLocations from "@cms/location/service/fetch-all-available-locations"
import FetchAllAvailableMentors from "@cms/mentor/service/fetch-all-available-mentors"
import { ExtendedSmartLink } from "@module/smart-link"
import { SeoIntegration } from "@module/seo/service"
import { ProductCardSetIntegration } from "@module/commerce/service"
import fetchAllRecordSlugs from "@cms/core/fetch-all-record-slugs"

export default async function getPage(slug: string): Promise<PageDataReturn> {
  const { data } = await CMS.client.query<CoursePageQueryReturn>({
    query: COURSE_PAGE_QUERY,
    variables: {
      slug,
    },
    fetchPolicy: CMS.fetchPolicy,
  })

  const {
    flexibleContent,
    introductionBanner,
    introductoryTitle,
    sections,
    introductoryDescription,
    inPageNavLinks = [],
    title,
    seo,
    promoBanner,
    ...courseOverviewData
  } = data.course

  const allFetchedLocations = await new FetchAllAvailableLocations().execute()
  const allFetchedMentors = await new FetchAllAvailableMentors().execute()

  const transformedContent = transformMultiReferenceRecords<TransformedData>(flexibleContent, coursePageComponents)
  const injectedShopifyProducts = await ProductCardSetIntegration.injectShopifyProducts(transformedContent)

  return {
    data: {
      ...data.course,
      seo: SeoIntegration.transformQueryData(seo),
      flexibleContent: injectedShopifyProducts,
      banner: transformMultiReferenceRecords<TransformedBannerData>([introductionBanner], courseBannerComponents),
      courseOverview: {
        ...CourseOverviewIntegration.transformQueryData({ ...courseOverviewData, __typename: "CourseRecord" }),
        title,
      },
      deliveryStructure: DeliveryStructureIntegration.transformQueryData({
        introductoryTitle,
        introductoryDescription,
        sections,
      }),
      allFetchedLocations,
      allFetchedMentors,
      promoBanner,
      inPageNavLinks: inPageNavLinks.map((value) => ExtendedSmartLink.transformQueryData(value)),
    },
  }
}

export async function getPagePaths() {
  const { data } = await CMS.client.query<TotalRecordQueryReturn>({
    query: COURSE_TOTAL_QUERY,
  })

  const slugResponse = await fetchAllRecordSlugs<CoursePageSlugQueryReturn>(
    COURSE_SLUGS_QUERY,
    "allCourses",
    data._allCoursesMeta.count,
  )

  const paths = slugResponse.map(({ slug }) => {
    return {
      params: {
        slug,
      },
    }
  })

  return {
    paths,
    fallback: false,
  }
}

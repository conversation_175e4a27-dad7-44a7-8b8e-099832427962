import { gql } from "@apollo/client"
import { collectFragments, composeFragments } from "@module/apricot/utils/fragment"
import { composeComponentsForQuery } from "@module/apricot/utils/compose-components"

import { ArticleContentIntegration } from "@module/article-content/service"

import { articlePageComponents } from "../config/components"

import type { GraphQLInputData, BlogBannerResponse } from "../types/articles"
import { ArticleContentStruct } from "@module/article-content/common/types"
import { SeoIntegration } from "@module/seo/service"
import { SeoStruct } from "@module/seo/common/types"
import { ArticleCardIntegration } from "@module/article"
import type { ArticleCardResponse } from "@module/article"

const mergedComponents = [...articlePageComponents, ArticleContentIntegration, SeoIntegration, ArticleCardIntegration]

export const ARTICLE_QUERY = gql`
  ${composeFragments(collectFragments(mergedComponents))}

  query Article($slug: String!) {
    article(filter: { slug: { eq: $slug } }) {
      id
      title
      publishDate
      featuredImage {
        url
        alt
      }
      categories {
        id
        title
        slug
      }
      flexibleContent {
        __typename
        ${composeComponentsForQuery(articlePageComponents)}
      }
      seo {
        ...${SeoIntegration.fragmentName}
      }
      ...${ArticleContentIntegration.fragmentName}
    }
    
    relatedArticles: allArticles(
      first: 10
      filter: { 
        _status: { eq: published }, 
        slug: { neq: $slug }
      }
      orderBy: publishDate_DESC
    ) {
      id
      ...${ArticleCardIntegration.fragmentName}
      categories {
        id
        title
        slug
      }
    }
  }
`

export const ARTICLE_SLUGS_QUERY = gql`
  query ArticleSlugs($skip: IntType!) {
    allArticles(filter: { _status: { eq: published } }, first: "100", skip: $skip) {
      id
      slug
    }
  }
`

export const ARTICLE_TOTAL_QUERY = gql`
  query ArticleTotal {
    _allArticlesMeta {
      count
    }
  }
`

export type QueryReturn = {
  article: BlogBannerResponse &
    ArticleContentStruct.DatoResponse & {
      flexibleContent: GraphQLInputData[]
      seo: SeoStruct.DatoResponse
    }
  relatedArticles: (ArticleCardResponse & {
    id: string
    categories: {
      id: string
      title: string
      slug: string
    }[]
  })[]
}

export type SlugQueryReturn = {
  allArticles: {
    id: string
    slug: string
  }[]
}

export type TotalRecordQueryReturn = {
  _allArticlesMeta: {
    count: number
  }
}

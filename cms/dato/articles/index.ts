import transformMultiReferenceRecords from "@module/apricot/utils/transform-multi-reference-records"
import CMS from "@cms/core"
import { DatoArticleBanner } from "@module/banner-article"
import { ArticleContentIntegration } from "@module/article-content/service"
import { SeoIntegration } from "@module/seo/service"
import { ArticleCardIntegration } from "@module/article"

import { ARTICLE_QUERY, ARTICLE_SLUGS_QUERY, ARTICLE_TOTAL_QUERY, TotalRecordQueryReturn } from "./query"
import { articlePageComponents } from "../config/components"

import type { QueryReturn, SlugQueryReturn } from "./query"
import type { TransformedData, PageDataReturn } from "../types/articles"
import { ProductCardSetIntegration } from "@module/commerce/service"
import fetchAllRecordSlugs from "@cms/core/fetch-all-record-slugs"

export default async function getArticlePage(slug: string): Promise<PageDataReturn> {
  const { data } = await CMS.client.query<QueryReturn>({
    query: ARTICLE_QUERY,
    variables: {
      slug,
    },
    fetchPolicy: CMS.fetchPolicy,
  })

  const { flexibleContent, content, ...bannerResponse } = data.article

  const categoryIds = data.article.categories?.map((cat) => cat.id) || []

  const relatedArticles = data.relatedArticles
    .filter((article) => {
      const articleCategories = article.categories || []
      return articleCategories.some((category) => categoryIds.includes(category.id))
    })
    .sort((a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime())
    .map((article) => ({
      id: article.id,
      ...ArticleCardIntegration.transformQueryData(article),
    }))

  const transformedContent = transformMultiReferenceRecords<TransformedData>(flexibleContent, articlePageComponents)
  const injectedShopifyProducts = await ProductCardSetIntegration.injectShopifyProducts(transformedContent)

  return {
    data: {
      slug,
      seo: SeoIntegration.transformQueryData(data.article.seo),
      banner: DatoArticleBanner.transformQueryData(bannerResponse),
      flexibleContent: injectedShopifyProducts,
      articleContent: ArticleContentIntegration.transformQueryData({ content }),
      relatedArticles,
    },
  }
}

export async function getArticlePagePaths() {
  const { data } = await CMS.client.query<TotalRecordQueryReturn>({
    query: ARTICLE_TOTAL_QUERY,
  })

  const slugResponse = await fetchAllRecordSlugs<SlugQueryReturn>(
    ARTICLE_SLUGS_QUERY,
    "allArticles",
    data._allArticlesMeta.count,
  )

  const paths = slugResponse.map((value) => {
    return {
      params: {
        slug: value.slug,
      },
    }
  })

  return {
    paths,
    fallback: false,
  }
}

import { gql } from "@apollo/client"
import type { GraphQLInputData } from "../types/homepage"
import { homepageComponents } from "../config/components"

import { collectFragments, composeFragments } from "@module/apricot/utils/fragment"
import { composeComponentsForQuery } from "@module/apricot/utils/compose-components"

import { SeoIntegration } from "@module/seo/service"
import { SeoStruct } from "@module/seo/common/types"

const HOMEPAGE_QUERY = gql`
${composeFragments(collectFragments([...homepageComponents, SeoIntegration]))}

query Homepage {
  page(filter: { promoteAsHomepage: { eq: true } }) {
    id
    title
    slug
    flexibleContent {
      __typename
      ${composeComponentsForQuery(homepageComponents)}
    }
    seo {
      ...${SeoIntegration.fragmentName}
    }
  }
}
`

export type PageQueryReturn = DatoPageQueryStructure<GraphQLInputData[]> & {
  page: {
    seo: SeoStruct.DatoResponse
  }
}

export default HOMEPAGE_QUERY

import transformMultiReferenceRecords from "@module/apricot/utils/transform-multi-reference-records"
import CMS from "../core"
import { TransformedData, PageDataReturn } from "../types/homepage"
import HOMEPAGE_QUERY, { PageQueryReturn } from "./query"
import { homepageComponents } from "../config/components"
import { SeoIntegration } from "@module/seo/service"
import { ProductCardSetIntegration } from "@module/commerce/service"

export default async function getHomepage(): Promise<PageDataReturn> {
  const { data } = await CMS.client.query<PageQueryReturn>({
    query: HOMEPAGE_QUERY,
    fetchPolicy: CMS.fetchPolicy,
  })

  const transformedContent = transformMultiReferenceRecords<TransformedData>(
    data.page.flexibleContent,
    homepageComponents,
  )
  const injectedShopifyProducts = await ProductCardSetIntegration.injectShopifyProducts(transformedContent)

  return {
    data: {
      ...data.page,
      seo: SeoIntegration.transformQueryData(data.page.seo),
      flexibleContent: injectedShopifyProducts,
    },
  }
}

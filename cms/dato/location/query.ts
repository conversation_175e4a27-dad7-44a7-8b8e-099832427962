import { gql } from "@apollo/client"
import { collectFragments, composeFragments } from "@module/apricot/utils/fragment"
import { composeComponentsForQuery } from "@module/apricot/utils/compose-components"

import { ExtendedSmartLink } from "@module/smart-link"
import { locationPageComponents } from "../config/components"
import { SeoIntegration } from "@module/seo/service"
import { SeoStruct } from "@module/seo/common/types"
import { IntakeClusterIntegration } from "@module/course"

import type { LocationBannerResponse, GraphQLInputData } from "../types/location"
import { IntakeClusterStruct } from "@module/course/common/types"

// TODO: Update to pull in intake cluster data
export const LOCATION_PAGE_QUERY = gql`
  ${composeFragments(
    collectFragments([...locationPageComponents, ExtendedSmartLink, SeoIntegration, IntakeClusterIntegration]),
  )}

  query LocationPage($slug: String!) {
    location(filter: { slug: { eq: $slug } }) {
      id
      title
      subtitle
      phoneNumber
      links {
        ...${ExtendedSmartLink.fragmentName}
      }
      featuredImage {
        url
        alt
      }
      branding {
        url
        alt
      }
      intakeClusters {
        ...${IntakeClusterIntegration.fragmentName}
      }
      locationAddress
      flexibleContent {
        __typename
        ${composeComponentsForQuery(locationPageComponents)}
      }
      seo{
        ...${SeoIntegration.fragmentName}
      }
    }
  }
`

export const LOCATION_PAGE_SLUGS_QUERY = gql`
  query LocationPageSlugs($skip: IntType!) {
    allLocations(filter: { _status: { eq: published } }, first: "100", skip: $skip) {
      id
      slug
    }
  }
`

export const LOCATION_TOTAL_QUERY = gql`
  query LocationTotalCount {
    _allLocationsMeta {
      count
    }
  }
`

export type LocationPageQueryReturn = {
  location: LocationBannerResponse & {
    id: string
    flexibleContent: GraphQLInputData[]
    seo: SeoStruct.DatoResponse
    intakeClusters: IntakeClusterStruct.DatoResponse[]
  }
}

export type LocationPageSlugQueryReturn = {
  allLocations: {
    slug: string
  }[]
}

export type TotalRecordQueryReturn = {
  _allLocationsMeta: {
    count: number
  }
}

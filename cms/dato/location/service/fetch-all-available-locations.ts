import { gql } from "@apollo/client"

import CMS from "@cms/core"

import { LocationCardIntegration } from "@module/location/service"
import type { CachedLocationsStruct } from "@module/redux/common/types"

export interface AllFetchedLocationsResponse {
  allLocations: CachedLocationsStruct.LocationResponse[]
  _allLocationsMeta: {
    count: number
  }
}

export type AllFetchedLocationsProperties = CachedLocationsStruct.Location[]

class FetchAllAvailableLocations {
  private limit: number = 100

  private get query() {
    return gql`
      ${LocationCardIntegration.fragment}

      query FetchAllAvailableLocations($skip: IntType!, $first: IntType!) {
        allLocations(filter: { _status: { eq: published } }, skip: $skip, first: $first) {
          id
          location {
            latitude
            longitude
          }
          coursesOffered {
            slug
          }
          ...${LocationCardIntegration.fragmentName}
        }
        _allLocationsMeta(filter: { _status: { eq: published } }) {
          count
        }
      }
    `
  }

  private executeQuery(skip: number) {
    return CMS.client.query<AllFetchedLocationsResponse>({
      query: this.query,
      variables: {
        skip,
        first: this.limit,
      },
      fetchPolicy: CMS.fetchPolicy,
    })
  }

  private async handleResultsHigherThanLimit(count: number): Promise<AllFetchedLocationsResponse["allLocations"]> {
    if (count > this.limit) {
      const additionalPages = Math.floor(count / this.limit)
      const queryPromises = []
      for (let i = 1; i <= additionalPages; i++) {
        queryPromises.push(this.executeQuery(this.limit * i))
      }

      const responses = await Promise.all(queryPromises)
      return responses.flatMap((value) => value.data.allLocations)
    }

    return []
  }

  public async execute(): Promise<AllFetchedLocationsProperties> {
    const {
      data: { _allLocationsMeta, allLocations },
    } = await this.executeQuery(0)

    const additionalLocations = await this.handleResultsHigherThanLimit(_allLocationsMeta.count)

    return [...allLocations, ...additionalLocations].map(({ id, ...response }) => ({
      id,
      ...LocationCardIntegration.transformQueryData(response),
    }))
  }
}

export default FetchAllAvailableLocations

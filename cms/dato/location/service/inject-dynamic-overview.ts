import { gql } from "@apollo/client"
import { collectFragments, composeFragments } from "@module/apricot/utils/fragment"

import CMS from "@cms/core"
import { LocationOverviewIntegration } from "@module/location/service"

import type { TransformedData } from "@cms/types/location"
import type { LocationOverviewStruct } from "@module/location/common/types"

const FETCH_OVERVIEW = gql`
  ${composeFragments(collectFragments([LocationOverviewIntegration]))}

  query FetchLocationOverview($id: ItemId) {
    location(filter: { id: { eq: $id } }) {
      id
      ...${LocationOverviewIntegration.fragmentName}
    }
  }
`

class InjectDynamicOverview {
  flexibleContent: TransformedData[]

  locationId: string

  public constructor(flexibleContent: TransformedData[], locationId: string) {
    this.flexibleContent = flexibleContent
    this.locationId = locationId
    return this
  }

  private executeQuery() {
    return CMS.client.query<{ location: LocationOverviewStruct.DatoResponse }>({
      query: <PERSON><PERSON><PERSON>_OVERVIEW,
      variables: {
        id: this.locationId,
      },
      fetchPolicy: CMS.fetchPolicy,
    })
  }

  private hasOverviewComponent(): boolean {
    return this.flexibleContent.some((value) => value.__typename === "LocationOverview")
  }

  private injectOverviewData(data: LocationOverviewStruct.Properties): TransformedData[] {
    return this.flexibleContent.map((value) => {
      if (value.__typename === "LocationOverview") {
        return {
          __typename: value.__typename,
          id: value.id,
          ...data,
        }
      }

      return value
    })
  }

  public async execute() {
    if (!this.hasOverviewComponent()) return this.flexibleContent

    const overviewResponse = await this.executeQuery()
    const overviewTransformed = LocationOverviewIntegration.transformQueryData(overviewResponse.data.location)

    return this.injectOverviewData(overviewTransformed)
  }
}

export default InjectDynamicOverview

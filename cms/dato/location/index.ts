import transformMultiReferenceRecords from "@module/apricot/utils/transform-multi-reference-records"
import CMS from "@cms/core"

import { LOCATION_PAGE_QUERY, LOCATION_PAGE_SLUGS_QUERY, LOCATION_TOTAL_QUERY, TotalRecordQueryReturn } from "./query"
import { locationPageComponents } from "../config/components"
import { DatoBannerLocationHero } from "@module/banner-location-hero"
import { SeoIntegration } from "@module/seo/service"

import type { TransformedData, PageDataReturn } from "../types/location"
import type { LocationPageSlugQueryReturn, LocationPageQueryReturn } from "./query"
import InjectDynamicMentors from "../mentor/service/inject-dynamic-mentors"
import InjectLocationCourses from "../course/service/inject-dynamic-location-courses"
import InjectDynamicOverview from "./service/inject-dynamic-overview"
import { ProductCardSetIntegration } from "@module/commerce/service"
import fetchAllRecordSlugs from "@cms/core/fetch-all-record-slugs"

export default async function getLocationPage(slug: string): Promise<PageDataReturn> {
  const { data } = await CMS.client.query<LocationPageQueryReturn>({
    query: LOCATION_PAGE_QUERY,
    variables: {
      slug,
    },
    fetchPolicy: CMS.fetchPolicy,
  })

  const { id, flexibleContent, seo, intakeClusters, ...bannerResponse } = data.location
  const transformedFlexibleContent = transformMultiReferenceRecords<TransformedData>(
    data.location.flexibleContent,
    locationPageComponents,
  )

  const withInjectedDynamicMentors = await new InjectDynamicMentors(transformedFlexibleContent, id).execute(
    intakeClusters,
  )
  const withInjectedLocationCourses = await new InjectLocationCourses(withInjectedDynamicMentors, id).execute()
  const withInjectedLocationOverview = await new InjectDynamicOverview(withInjectedLocationCourses, id).execute()
  const withInjectedProducts = await ProductCardSetIntegration.injectShopifyProducts(withInjectedLocationOverview)

  const injectedIntakeClusters = withInjectedProducts.map((value) => {
    if (value.__typename === "IntakeCallToAction") {
      return { ...value, intakeClusters }
    }
    return value
  })

  return {
    data: {
      banner: DatoBannerLocationHero.transformQueryData(bannerResponse),
      flexibleContent: injectedIntakeClusters,
      seo: SeoIntegration.transformQueryData(seo),
    },
  }
}

export async function getLocationPagePaths() {
  const { data } = await CMS.client.query<TotalRecordQueryReturn>({
    query: LOCATION_TOTAL_QUERY,
  })

  const slugResponse = await fetchAllRecordSlugs<LocationPageSlugQueryReturn>(
    LOCATION_PAGE_SLUGS_QUERY,
    "allLocations",
    data._allLocationsMeta.count,
  )

  const paths = slugResponse.map(({ slug }) => {
    return {
      params: {
        slug,
      },
    }
  })

  return {
    paths,
    fallback: false,
  }
}

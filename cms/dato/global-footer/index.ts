import { gql } from "@apollo/client"
import { collectFragments, composeFragments } from "@module/apricot/utils/fragment"
import { ExtendedFooterIntegration } from "@module/footer"
import type { FooterIntegration } from "@module/footer"
import CMS from "../core"

const FOOTER_QUERY = gql`
${composeFragments(collectFragments([ExtendedFooterIntegration]))}

query SiteFooter {
  globalFooter {
    id
    ...${ExtendedFooterIntegration.fragmentName}
  }
}
`

type GraphQLResponse = {
  globalFooter: FooterIntegration.DatoResponse
}

const getGlobalFooter = async () => {
  const { data } = await CMS.client.query<GraphQLResponse>({
    query: FOOTER_QUERY,
    fetchPolicy: CMS.fetchPolicy,
  })

  return ExtendedFooterIntegration.transformQueryData(data.globalFooter)
}

export default getGlobalFooter

import { gql } from "@apollo/client"
import { collectFragments, composeFragments } from "@module/apricot/utils/fragment"

import CMS from "../core"

import { EnquireFormIntegration } from "@module/enquire-form/service"
import { EnquireFormStruct } from "@module/enquire-form/common/types"

const ENQUIRE_FORM_QUERY = gql`
${composeFragments(collectFragments([EnquireFormIntegration]))}

query EnquireForm {
  globalEnquiryForm {
    ...${EnquireFormIntegration.fragmentName}
  }
}
`

type GraphQLResponse = {
  globalEnquiryForm: EnquireFormStruct.DatoResponse
}

const getEnquireForm = async () => {
  const { data } = await CMS.client.query<GraphQLResponse>({
    query: ENQUIRE_FORM_QUERY,
    fetchPolicy: CMS.fetchPolicy,
  })

  return EnquireFormIntegration.transformQueryData(data.globalEnquiryForm)
}

export default getEnquireForm

import { gql } from "@apollo/client"
import { collectFragments, composeFragments } from "@module/apricot/utils/fragment"
import { composeComponentsForQuery } from "@module/apricot/utils/compose-components"

import { locationLandingBannerComponents, locationLandingComponents } from "@cms/config/components"
import { ExtendedSmartLink } from "@module/smart-link"
import { SeoIntegration } from "@module/seo/service"

const mergedComponents = [
  ...locationLandingBannerComponents,
  ...locationLandingComponents,
  ExtendedSmartLink,
  SeoIntegration,
]

export const LOCATION_LANDING_QUERY = gql`
  ${composeFragments(collectFragments(mergedComponents))}

  query LocationsLanding {
    locationsLanding {
      introductionBanner {
        __typename
        ${composeComponentsForQuery(locationLandingBannerComponents)}
      }
      flexibleContent {
        __typename
        ${composeComponentsForQuery(locationLandingComponents)}
      }
      mixedCampusPin {
        url
      }
      onCampusPin {
        url
      }
      withAiptMentorPin {
        url
      }
      withAiptMentorTooltip
      onCampusTooltip
      noLocationsFoundActions {
        ...${ExtendedSmartLink.fragmentName}
      }
      filterByCourses {
        id
        title
      }
      seo {
        ...${SeoIntegration.fragmentName}
      }
    }
  }
`

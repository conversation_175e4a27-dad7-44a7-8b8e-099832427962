import transformMultiReferenceRecords from "@module/apricot/utils/transform-multi-reference-records"
import cms from "@cms/core/cms"

import { locationLandingBannerComponents, locationPageComponents } from "@cms/config/components"

import { ExtendedSmartLink } from "@module/smart-link"
import { LOCATION_LANDING_QUERY } from "./query"

import FetchRelevantMentors from "@module/interactive-map/service/fetch-relevant-mentors"
import FetchRelevantLocations from "@module/interactive-map/service/fetch-relevant-locations"
import { SeoIntegration } from "@module/seo/service"
import { ProductCardSetIntegration } from "@module/commerce/service"

import type { PageDataReturn, QueryReturn, BannerTransformedData, TransformedData } from "@cms/location-landing/types"

export async function getLocationLandingData(): Promise<PageDataReturn> {
  const { data } = await cms.client.query<QueryReturn>({
    query: LOCATION_LANDING_QUERY,
    fetchPolicy: cms.fetchPolicy,
  })

  const { introductionBanner, flexibleContent, noLocationsFoundActions, seo, ...response } = data.locationsLanding

  const courseIds = response.filterByCourses.map(({ id }) => id)
  const allLocations = await new FetchRelevantLocations(courseIds).execute()
  const allMentors = await new FetchRelevantMentors(courseIds).execute()

  const transformedContent = transformMultiReferenceRecords<TransformedData>(flexibleContent, locationPageComponents)
  const injectedShopifyProducts = await ProductCardSetIntegration.injectShopifyProducts(transformedContent)

  return {
    data: {
      ...response,
      introductionBanner: transformMultiReferenceRecords<BannerTransformedData>(
        introductionBanner,
        locationLandingBannerComponents,
      ),
      flexibleContent: injectedShopifyProducts,
      noLocationsFoundActions: noLocationsFoundActions.map((value) => ExtendedSmartLink.transformQueryData(value)),
      allLocations,
      allMentors,
      seo: SeoIntegration.transformQueryData(seo),
    },
  }
}

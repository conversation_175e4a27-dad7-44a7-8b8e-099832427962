import type { SmartLinkProperties } from "@module/apricot/components/smart-link"
import type { ExtractArrayTypes } from "@module/apricot/types/utility"
import type { InteractiveMap } from "@module/interactive-map/common/types"
import { SeoStruct } from "@module/seo/common/types"
import type { ExtendedDatoResponse } from "@module/smart-link/common/types"

import { locationLandingBannerComponents, locationLandingComponents } from "../config/components"

type ExtractedBannerTransformActions = ExtractArrayTypes<typeof locationLandingBannerComponents>["transformQueryData"]
type ExtractedPageTransformActions = ExtractArrayTypes<typeof locationLandingComponents>["transformQueryData"]

export type BannerTransformedData = ReturnType<ExtractedBannerTransformActions>
export type TransformedData = ReturnType<ExtractedPageTransformActions>

export type BannerGraphQLInputData = Parameters<ExtractedBannerTransformActions>[0]
export type GraphQLInputData = Parameters<ExtractedPageTransformActions>[0]

export type PageDataReturn = {
  data: {
    introductionBanner: BannerTransformedData[]
    flexibleContent: TransformedData[]
    filterByCourses: GenericList[]
    noLocationsFoundActions: SmartLinkProperties[]
    onCampusTooltip: string
    withAiptMentorTooltip: string
    mixedCampusPin: { url: string }
    onCampusPin: { url: string }
    withAiptMentorPin: { url: string }
    allLocations: InteractiveMap.MapLocation[]
    allMentors: InteractiveMap.MapLocation[]
    seo: SeoStruct.Properties
  }
}

export type QueryReturn = {
  locationsLanding: {
    introductionBanner: BannerGraphQLInputData[]
    flexibleContent: GraphQLInputData[]
    filterByCourses: GenericList[]
    noLocationsFoundActions: ExtendedDatoResponse[]
    onCampusTooltip: string
    withAiptMentorTooltip: string
    mixedCampusPin: { url: string }
    onCampusPin: { url: string }
    withAiptMentorPin: { url: string }
    seo: SeoStruct.DatoResponse
  }
}

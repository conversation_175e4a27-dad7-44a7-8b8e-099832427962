import { Secrets } from "@module/apricot/types/cms"

class DatoSecrets implements Secrets {
  public get baseUrl(): string {
    if (!process.env.DATO_ENDPOINT) {
      throw new Error("DATO_ENDPOINT has not been set")
    }
    return process.env.DATO_ENDPOINT
  }

  public get token(): string {
    if (!process.env.DATO_API_TOKEN) {
      throw new Error("DATO_API_TOKEN has not been set")
    }
    return process.env.DATO_API_TOKEN
  }
}

export default DatoSecrets

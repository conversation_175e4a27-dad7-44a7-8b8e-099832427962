import type { C<PERSON>, Secrets, GraphQLCMS } from "@module/apricot/types/cms"
import createApolloClient from "@module/apricot/utils/create-apollo-client"
import DatoSecrets from "./secrets"
import type { FetchPolicy } from "@apollo/client"

class DatoCMS implements CMS, GraphQLCMS {
  secrets: Secrets = new DatoSecrets()

  client = createApolloClient(this.getEndpoint(), this.secrets.token)

  public getEndpoint(): string {
    const baseEndpoint = [this.secrets.baseUrl, process.env.DATO_ENVIRONMENT]
    return baseEndpoint.join("/")
  }

  public get fetchPolicy(): FetchPolicy {
    return process.env.NODE_ENV === "development" ? "network-only" : "cache-first"
  }
}

const datoCMS = new DatoCMS()
export default datoCMS

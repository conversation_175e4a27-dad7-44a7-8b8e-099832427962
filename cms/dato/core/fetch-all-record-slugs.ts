import type { DocumentNode, ApolloQueryResult } from "@apollo/client"
import cms from "./cms"

const DATO_LIMIT = 100

interface SlugResponse {
  slug: string[]
}

interface QueryResponse {
  [key: string]: {
    slug: string
  }[]
}

function executeQuery<T>(query: DocumentNode, skip: number): Promise<ApolloQueryResult<T>> {
  return cms.client.query({
    query,
    variables: {
      skip: skip,
    },
  })
}

export default async function fetchAllRecordSlugs<T extends QueryResponse>(
  query: DocumentNode,
  allRecordsKey: keyof T,
  totalCount: number,
): Promise<SlugResponse[]> {
  const recordRequests = [executeQuery<T>(query, 0)]

  if (totalCount > DATO_LIMIT) {
    const requestsRequired = Math.floor(totalCount / DATO_LIMIT)
    for (let i = 1; i <= requestsRequired; i++) {
      recordRequests.push(executeQuery<T>(query, DATO_LIMIT * i))
    }
  }

  const responses = await Promise.all(recordRequests)

  const allRecords = responses.flatMap((value) => value.data[allRecordsKey])
  return allRecords.flatMap((value) => ({
    slug: value.slug.split("/"),
  }))
}

import React from "react"
import Link from "next/link"
import { LocationCard } from "@module/location/ui"
import { Grid, Column } from "@module/apricot/components/grid"
import { Box } from "@module/apricot/components/box"
import { Button } from "@module/apricot/components/button"
import type { CachedLocationsStruct } from "@module/redux/common/types/cached-locations"

interface LocationsListProps {
  locations: CachedLocationsStruct.Location[]
  currentPage: number
  totalPages: number
  totalLocations: number
}

export default function LocationsList({ locations, currentPage, totalPages, totalLocations }: LocationsListProps) {
  return (
    <Box css={{ marginBottom: "$xl" }}>
      <Box css={{ marginBottom: "$lg", textAlign: "center" }}>
        <p>
          Showing {locations.length} of {totalLocations} locations
        </p>
      </Box>

      {/* Locations Grid */}
      <Grid>
        {locations.map((location) => (
          <Column key={location.id} col={{ "@initial": 12, "@md": 6, "@lg": 4 }}>
            <LocationCard {...location} />
          </Column>
        ))}
      </Grid>

      {/* Pagination */}
      {totalPages > 1 && (
        <Box
          css={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            gap: "$sm",
            marginTop: "$xl",
          }}
        >
          {/* Previous Page Link */}
          {currentPage > 1 && (
            <Link href={currentPage === 2 ? "/locations" : `/locations/page/${currentPage - 1}`} passHref>
              <Button as="a" variant="secondary">
                Previous
              </Button>
            </Link>
          )}

          {/* Page Numbers */}
          <Box css={{ display: "flex", gap: "$xs", alignItems: "center" }}>
            {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
              let pageNum: number

              if (totalPages <= 7) {
                pageNum = i + 1
              } else if (currentPage <= 4) {
                pageNum = i + 1
              } else if (currentPage >= totalPages - 3) {
                pageNum = totalPages - 6 + i
              } else {
                pageNum = currentPage - 3 + i
              }

              const isCurrentPage = pageNum === currentPage
              const href = pageNum === 1 ? "/locations" : `/locations/page/${pageNum}`

              return (
                <Link key={pageNum} href={href} passHref>
                  <Button as="a" variant={isCurrentPage ? "primary" : "secondary"} size="lg" css={{ minWidth: "40px" }}>
                    {pageNum}
                  </Button>
                </Link>
              )
            })}
          </Box>

          {/* Next Page Link */}
          {currentPage < totalPages && (
            <Link href={`/locations/page/${currentPage + 1}`} passHref>
              <Button as="a" variant="secondary">
                Next
              </Button>
            </Link>
          )}
        </Box>
      )}
    </Box>
  )
}

"use client"

import React from "react"
import dynamic from "next/dynamic"
import type { PageDataReturn } from "@cms/location-landing/types"

// Dynamic import with ssr: false to ensure client-side only rendering
const InteractiveMap = dynamic(() => import("@module/interactive-map"), {
  ssr: false,
  loading: () => (
    <div
      style={{
        height: "500px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#f5f5f5",
        borderRadius: "8px",
      }}
    >
      Loading map...
    </div>
  ),
})

type InteractiveMapProperties = Omit<PageDataReturn["data"], "introductionBanner" | "flexibleContent" | "seo">

export default function LocationsMapClient(props: InteractiveMapProperties) {
  return <InteractiveMap {...props} />
}

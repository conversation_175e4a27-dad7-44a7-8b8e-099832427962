import { GraphQLClient } from "graphql-request"

export const createClient = (includeDrafts = false) => {
  const token = process.env.DATO_API_TOKEN
  if (!token) {
    throw new Error("DATO_API_TOKEN has not been set")
  }

  const headers: Record<string, string> = {
    Authorization: `Bearer ${token}`,
  }

  if (includeDrafts) {
    headers["X-Include-Drafts"] = "true"
  }

  const client = new GraphQLClient("https://graphql.datocms.com/", {
    headers,
  })
  return client
}
